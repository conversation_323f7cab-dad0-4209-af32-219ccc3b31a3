# Cline插件安装配置指南

> 详细的Cline AI编程助手安装和配置教程，包含API配置和使用技巧

## 🔧 系统要求

- VS Code 1.74.0 或更高版本
- 稳定的网络连接（内网环境）
- 平台API密钥（在API密钥管理页面获取）
- 操作系统：Windows 10+、macOS 10.15+、Linux

## 📦 获取安装包

1. 访问平台"工具下载中心"页面
2. 确保已登录平台账户
3. 点击"Cline插件"的"安装指南"按钮
4. 在本页面上方下载.vsix安装包文件
5. 将安装包保存到本地目录

## 🚀 安装步骤

1. 打开VS Code编辑器
2. 按 `Ctrl+Shift+P` (Mac: `Cmd+Shift+P`) 打开命令面板
3. 输入 "Extensions: Install from VSIX" 并选择
4. 浏览并选择下载的Cline插件.vsix文件
5. 等待安装完成，VS Code会显示安装成功提示
6. 重启VS Code使插件生效

![VS Code扩展安装](./images/vscode-install-extension.png)

## ⚙️ API配置

1. VS Code → 文件 → 首选项 → 设置
2. 搜索 "Cline" 找到插件设置
3. 模型供应商类型：选择 "OpenAI Compatible"
4. API基础URL：`http://*************:14000` (对于部分插件，可尝试`http://*************:14000/v1`)
5. 模型名称：`qwen3-235b-a22b`
6. API密钥：粘贴您在平台生成的密钥
7. 点击"测试连接"验证配置

```json
{
  "cline.apiProvider": "openai-compatible",
  "cline.apiBaseUrl": "http://*************:14000",
  "cline.model": "qwen3-235b-a22b",
  "cline.apiKey": "your-api-key-here"
}
```

## 💡 首次使用

1. 在VS Code中打开您的项目文件夹
2. 点击左侧活动栏的Cline图标
3. 用自然语言描述您的编程需求
4. 仔细预览AI建议的代码更改
5. 选择批准或拒绝每个建议的操作
6. 可以随时撤销或修改AI的建议

![Cline界面示例](./images/cline-interface.png)

## 📹 安装演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/cline-installation-demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## ❓ 常见问题

### Q: 插件安装后无法启动怎么办？
A: 请确保重启VS Code，并检查VS Code版本是否符合要求。

### Q: API连接失败怎么办？
A: 检查API地址配置是否正确，确认网络连接正常。

### Q: 如何更新插件？
A: 重新下载最新版本的.vsix文件，然后重复安装步骤即可。

---

*最后更新时间：2024年12月* 