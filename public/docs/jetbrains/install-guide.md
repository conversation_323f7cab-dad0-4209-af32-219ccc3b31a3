# JetBrains AI Assistant安装指南

> JetBrains系列IDE的AI助手插件详细安装配置教程，支持IntelliJ、PyCharm等

## 💻 支持的IDE

JetBrains AI Assistant支持以下IDE：

| IDE | 版本要求 | 支持状态 |
|-----|---------|----------|
| **IntelliJ IDEA** | 2023.2+ | ✅ 完全支持 |
| **PyCharm** | 2023.2+ | ✅ 完全支持 |
| **WebStorm** | 2023.2+ | ✅ 完全支持 |
| **Android Studio** | 2023.2+ | ✅ 完全支持 |
| **CLion** | 2023.2+ | ✅ 完全支持 |
| **GoLand** | 2023.2+ | ✅ 完全支持 |
| **Rider** | 2023.2+ | ✅ 完全支持 |
| **DataGrip** | 2023.2+ | ⚠️ 部分支持 |
| **PhpStorm** | 2023.2+ | ✅ 完全支持 |

![JetBrains IDE支持](./images/jetbrains-ide-support.png)

## 📦 获取安装包

### 方式一：平台下载
1. 访问平台"工具下载中心"页面
2. 确保已登录平台账户
3. 点击"JetBrains AI Assistant"的"安装指南"按钮
4. 联系系统管理员获取插件安装包
5. 将安装包保存到本地目录

### 方式二：内部分发
某些组织可能通过内部渠道分发插件包：
- 查看公司内部工具分发页面
- 联系IT部门或开发团队负责人
- 从企业插件仓库下载

## 🚀 安装步骤

### 标准安装流程

#### 1. 打开IDE设置
- 启动您的JetBrains IDE
- 在菜单栏选择：
  - **Windows/Linux**: `File` → `Settings`
  - **macOS**: `IDE名称` → `Preferences`
- 或使用快捷键：`Ctrl+Alt+S` (Windows/Linux) / `Cmd+,` (macOS)

#### 2. 进入插件管理
1. 在设置窗口左侧，找到并点击 `Plugins`
2. 点击右上角的齿轮图标 ⚙️
3. 选择 `Install Plugin from Disk...`

![插件安装界面](./images/jetbrains-plugin-install.png)

#### 3. 选择插件文件
1. 在文件选择对话框中，浏览到下载的AI Assistant插件文件
2. 选择 `.zip` 或 `.jar` 格式的插件文件
3. 点击 `OK` 确认选择

#### 4. 完成安装
1. IDE会显示安装进度
2. 安装完成后，会出现确认对话框
3. 点击 `Restart IDE` 重启IDE使插件生效

### 企业环境安装

#### 离线安装
```bash
# 1. 下载插件到本地
# 2. 通过IDE设置安装
File → Settings → Plugins → Install from Disk

# 3. 或使用命令行安装（部分IDE支持）
idea installPlugin /path/to/ai-assistant-plugin.zip
```

#### 批量部署
企业管理员可以使用JetBrains插件部署工具：
```xml
<!-- plugins.xml 配置示例 -->
<plugins>
  <plugin id="ai-assistant" 
          url="http://internal-repo/ai-assistant-plugin.zip" 
          version="1.0.0" />
</plugins>
```

## ⚙️ 配置设置

### 基础API配置

#### 1. 打开AI Assistant设置
安装并重启IDE后：
1. 进入 `Settings/Preferences`
2. 找到 `AI Assistant` 或 `AI Configuration` 选项
3. 点击进入配置页面

#### 2. API连接配置
```json
{
  "apiEndpoint": "http://*************:14000",
  "model": "qwen3-235b-a22b",
  "apiKey": "your-platform-api-key",
  "timeout": 30000,
  "maxRetries": 3
}
```

配置参数说明：
- **API端点**: `http://*************:14000`
- **模型名称**: `qwen3-235b-a22b`
- **API密钥**: 从平台API密钥管理页面获取
- **超时时间**: 建议设置为30秒
- **重试次数**: 建议设置为3次

![API配置界面](./images/jetbrains-api-config.png)

### 高级配置选项

#### 代码补全设置
```json
{
  "codeCompletion": {
    "enabled": true,
    "autoTrigger": true,
    "delay": 500,
    "maxSuggestions": 5,
    "contextLength": 2000
  }
}
```

#### 聊天功能设置
```json
{
  "chatInterface": {
    "enabled": true,
    "contextAware": true,
    "maxHistory": 100,
    "autoSave": true
  }
}
```

#### 隐私和安全设置
```json
{
  "privacy": {
    "sendCodeContext": true,
    "logInteractions": false,
    "shareUsageStats": false,
    "encryptCommunication": true
  }
}
```

## 💡 首次使用

### 启动AI助手

#### 方法一：通过工具窗口
1. 在IDE底部或右侧寻找 `AI Chat` 工具窗口
2. 如果没有看到，可以通过 `View` → `Tool Windows` → `AI Chat` 打开
3. 点击工具窗口标签即可开始使用

#### 方法二：通过快捷键
- 默认快捷键：`Ctrl+Shift+A` (Windows/Linux) / `Cmd+Shift+A` (macOS)
- 可在 `Settings` → `Keymap` 中自定义快捷键

#### 方法三：通过菜单
- `Tools` → `AI Assistant` → `Open Chat`

### 基本使用流程

#### 1. 验证连接
首次使用时，AI助手会自动测试API连接：
```
✅ 连接成功 - qwen3-235b-a22b 模型可用
🔧 配置完成 - 代码补全已启用
💬 聊天就绪 - 可以开始对话
```

#### 2. 简单测试
在聊天窗口输入测试消息：
```
用户：你好，请介绍一下你的功能
AI：你好！我是JetBrains AI助手，可以帮助您：
- 智能代码生成和补全
- 代码分析和重构建议
- 错误诊断和修复
- 技术问题解答
- 代码文档生成
```

#### 3. 代码相关操作
选中代码片段后，可以：
- 右键选择 `AI Assistant` → `Explain Code`
- 右键选择 `AI Assistant` → `Generate Tests`
- 右键选择 `AI Assistant` → `Refactor Code`

![使用示例](./images/jetbrains-usage-example.png)

## 🔧 功能验证

### 代码补全测试
```java
// 在Java文件中输入以下代码
public class Calculator {
    public int add(int a, int b) {
        // AI应该自动建议：return a + b;
    }
}
```

### 聊天功能测试
在AI Chat窗口中测试：
```
1. 解释代码："请解释这个函数的作用"
2. 生成代码："创建一个用户登录的API接口"
3. 调试帮助："为什么我的代码出现空指针异常？"
```

### 重构建议测试
选中重复代码，AI助手应该建议：
- 提取方法
- 提取常量
- 简化表达式
- 优化性能

## 📹 安装演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/jetbrains-installation-guide.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## ⚠️ 常见问题

### Q: 插件安装后在IDE中找不到？
**A**: 请尝试以下解决方案：
1. 确认已重启IDE
2. 检查插件是否在 `Settings` → `Plugins` → `Installed` 中显示为已启用
3. 查看IDE兼容性：确保IDE版本≥2023.2
4. 检查日志：`Help` → `Show Log in Explorer/Finder`

### Q: API连接失败？
**A**: 请检查以下配置：
1. **网络连接**：ping ************* 检查网络可达性
2. **API地址**：确认使用 `http://*************:14000`
3. **API密钥**：验证密钥格式和有效性
4. **防火墙**：检查企业防火墙设置

### Q: 代码补全不工作？
**A**: 可能的原因和解决方案：
1. **功能未启用**：Settings → AI Assistant → 确保代码补全已开启
2. **延迟设置**：调整触发延迟时间（建议500ms）
3. **IDE插件冲突**：禁用其他AI代码补全插件
4. **项目类型**：确认当前项目类型被支持

### Q: 如何卸载插件？
**A**: 卸载步骤：
1. `Settings` → `Plugins` → `Installed`
2. 找到AI Assistant插件
3. 点击插件右侧的下拉箭头
4. 选择 `Uninstall`
5. 重启IDE

## 🚀 性能优化建议

### IDE性能优化
```properties
# 在IDE的自定义VM选项中添加：
-Xms2g
-Xmx4g
-XX:ReservedCodeCacheSize=512m
-XX:+UseConcMarkSweepGC
```

### 插件配置优化
```json
{
  "performance": {
    "enableParallelProcessing": true,
    "cacheSize": "500MB",
    "backgroundProcessing": true,
    "throttleRequests": true
  }
}
```

## 📊 使用统计

### 查看插件状态
在IDE中查看AI助手状态：
1. `Help` → `AI Assistant Status`
2. 查看连接状态、API使用量、错误日志等信息

### 使用情况报告
```
📊 本月使用统计：
- API调用次数：1,234
- 代码补全建议：5,678
- 聊天对话：89
- 成功率：94.5%
```

---

*最后更新时间：2024年12月* 