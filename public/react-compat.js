// React compatibility patch - must be loaded before any React code
// This fixes the AsyncMode issue for CopilotKit and other libraries

(function() {
  'use strict';
  
  // Wait for React to be available
  function patchReact() {
    if (typeof window !== 'undefined' && window.React) {
      const React = window.React;
      
      // Add missing deprecated APIs
      if (!React.AsyncMode) {
        React.AsyncMode = React.Fragment;
      }
      
      if (!React.unstable_AsyncMode) {
        React.unstable_AsyncMode = React.Fragment;
      }
      
      if (!React.ConcurrentMode) {
        React.ConcurrentMode = React.Fragment;
      }
      
      if (!React.unstable_ConcurrentMode) {
        React.unstable_ConcurrentMode = React.Fragment;
      }
      
      console.debug('React compatibility patch applied');
      return true;
    }
    return false;
  }
  
  // Try to patch immediately
  if (!patchReact()) {
    // If React isn't available yet, wait for it
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max
    
    const checkInterval = setInterval(function() {
      attempts++;
      
      if (patchReact() || attempts >= maxAttempts) {
        clearInterval(checkInterval);
        if (attempts >= maxAttempts) {
          console.warn('React compatibility patch: React not found after 5 seconds');
        }
      }
    }, 100);
  }
})();