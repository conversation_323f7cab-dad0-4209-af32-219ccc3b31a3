#!/bin/bash

echo "🔒 配置OpenVSCode HTTPS支持"
echo "=================================="

# 创建SSL证书目录
CERT_DIR="./ssl-certs"
mkdir -p "$CERT_DIR"

echo "📋 生成自签名SSL证书..."

# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout "$CERT_DIR/key.pem" -out "$CERT_DIR/cert.pem" -days 365 -nodes -subj "/C=CN/ST=State/L=City/O=Organization/CN=*************"

if [ $? -eq 0 ]; then
    echo "✅ SSL证书生成成功"
    echo "📁 证书位置: $CERT_DIR/"
    echo "   - 私钥: $CERT_DIR/key.pem"
    echo "   - 证书: $CERT_DIR/cert.pem"
else
    echo "❌ SSL证书生成失败，请检查openssl是否安装"
    exit 1
fi

echo ""
echo "🔧 OpenVSCode HTTPS启动命令："
echo "--------------------------------"
echo "Docker方式:"
echo "docker run -it --init -p 3667:3000 \\"
echo "  -v $CERT_DIR:/certs \\"
echo "  -e OPENVSCODE_SERVER_ROOT=/home/<USER>"
echo "  -e CONNECTION_TOKEN=tk-ynnx-llm \\"
echo "  -e OPENVSCODE_USE_SSL=true \\"
echo "  -e OPENVSCODE_SSL_CERT=/certs/cert.pem \\"
echo "  -e OPENVSCODE_SSL_KEY=/certs/key.pem \\"
echo "  gitpod/openvscode-server"

echo ""
echo "非Docker方式 (需要修改启动脚本):"
echo "openvscode-server --host ************* --port 3667 \\"
echo "  --connection-token tk-ynnx-llm \\"
echo "  --cert $CERT_DIR/cert.pem \\"
echo "  --cert-key $CERT_DIR/key.pem"

echo ""
echo "⚠️  重要提醒："
echo "1. 使用自签名证书，浏览器会显示安全警告"
echo "2. 需要在浏览器中手动信任这个证书"
echo "3. 访问地址将变为: https://*************:3667"

echo ""
echo "🌐 浏览器信任证书步骤："
echo "1. 访问 https://*************:3667"
echo "2. 点击 '高级' 或 'Advanced'"
echo "3. 点击 '继续前往*************(不安全)'"
echo "4. 或者将证书添加到浏览器受信任列表" 