#!/bin/bash

# 生成WebIDE的自签名SSL证书
# 用于解决crypto.subtle安全上下文问题

set -e

echo "🔐 开始生成WebIDE的自签名SSL证书..."

# 创建SSL目录
SSL_DIR="./nginx/ssl"
mkdir -p "$SSL_DIR"

# 证书配置
CERT_NAME="webide"
DAYS=365
COUNTRY="CN"
STATE="Yunnan"
CITY="Kunming"
ORG="YNNX"
UNIT="AI Platform"
COMMON_NAME="localhost"

# 生成私钥
echo "📝 生成私钥..."
openssl genrsa -out "$SSL_DIR/$CERT_NAME.key" 2048

# 生成证书签名请求
echo "📋 生成证书签名请求..."
openssl req -new -key "$SSL_DIR/$CERT_NAME.key" -out "$SSL_DIR/$CERT_NAME.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$UNIT/CN=$COMMON_NAME"

# 生成自签名证书
echo "🔏 生成自签名证书..."
openssl x509 -req -in "$SSL_DIR/$CERT_NAME.csr" -signkey "$SSL_DIR/$CERT_NAME.key" -out "$SSL_DIR/$CERT_NAME.crt" -days $DAYS

# 清理临时文件
rm "$SSL_DIR/$CERT_NAME.csr"

# 设置权限
chmod 600 "$SSL_DIR/$CERT_NAME.key"
chmod 644 "$SSL_DIR/$CERT_NAME.crt"

echo "✅ SSL证书生成完成！"
echo "📁 证书位置: $SSL_DIR/"
echo "🔑 私钥: $CERT_NAME.key"
echo "📜 证书: $CERT_NAME.crt"
echo "⏰ 有效期: $DAYS 天"
echo ""
echo "⚠️  注意: 这是自签名证书，浏览器会显示安全警告"
echo "💡 使用时请点击 '高级' -> '继续访问localhost(不安全)'"
echo ""
echo "🔗 HTTPS访问地址: https://localhost:3668"