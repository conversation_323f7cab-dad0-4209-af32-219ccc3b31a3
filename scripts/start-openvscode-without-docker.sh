#!/bin/bash
# OpenVSCode服务器启动脚本 - 不使用Docker版本
# 解决403 Forbidden错误

# 配置变量
OPENVSCODE_PORT=3667
OPENVSCODE_HOST=0.0.0.0
CONNECTION_TOKEN="tk-ynnx-llm"
WORKSPACE_PATH="/tmp/openvscode-workspace"

# 创建工作区目录
mkdir -p "$WORKSPACE_PATH"

echo "🚀 启动OpenVSCode服务器（不使用Docker）..."
echo "端口: $OPENVSCODE_PORT"
echo "主机: $OPENVSCODE_HOST"
echo "Token: $CONNECTION_TOKEN"
echo "工作区: $WORKSPACE_PATH"

# 检查是否已安装OpenVSCode服务器
if ! command -v openvscode-server &> /dev/null; then
    echo "❌ OpenVSCode服务器未安装"
    echo "请先安装OpenVSCode服务器："
    echo "1. 从 https://github.com/gitpod-io/openvscode-server/releases 下载最新版本"
    echo "2. 解压并将 openvscode-server 添加到 PATH"
    echo "或者使用npm安装: npm install -g @gitpod/openvscode-server"
    exit 1
fi

# 启动OpenVSCode服务器
echo "正在启动服务器..."
openvscode-server \
    --host $OPENVSCODE_HOST \
    --port $OPENVSCODE_PORT \
    --connection-token $CONNECTION_TOKEN \
    --without-connection-token \
    --accept-server-license-terms \
    --folder $WORKSPACE_PATH 