#!/bin/bash

echo "🔄 配置本地代理到OpenVSCode"
echo "=================================="

# 检查nginx是否安装
if ! command -v nginx &> /dev/null; then
    echo "📦 安装nginx..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y nginx
    elif command -v yum &> /dev/null; then
        sudo yum install -y nginx
    else
        echo "❌ 请手动安装nginx"
        exit 1
    fi
fi

echo "✅ nginx已安装"

# 创建nginx配置
NGINX_CONF="/tmp/openvscode-proxy.conf"
cat > "$NGINX_CONF" << 'EOF'
server {
    listen 3667;
    server_name localhost;

    # 代理所有请求到OpenVSCode服务器
    location / {
        proxy_pass http://*************:3667;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

echo "📁 创建nginx配置: $NGINX_CONF"

echo ""
echo "🚀 启动nginx代理:"
echo "--------------------------------"
echo "sudo nginx -c $NGINX_CONF -g 'daemon off;'"
echo ""
echo "或者后台运行:"
echo "sudo nginx -c $NGINX_CONF"

echo ""
echo "🌐 代理配置完成后访问地址:"
echo "http://localhost:3667?tkn=tk-ynnx-llm"

echo ""
echo "⚠️  注意事项:"
echo "1. 需要以sudo权限运行nginx"
echo "2. 确保端口3667未被占用"
echo "3. OpenVSCode服务器需要在*************:3667运行"

echo ""
echo "🛑 停止代理:"
echo "sudo nginx -s stop" 