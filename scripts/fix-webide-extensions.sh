#!/bin/bash

echo "🔧 修复 OpenVSCode 插件页面显示问题"
echo "====================================="

# 检查当前状态
echo "1️⃣ 检查当前配置状态..."

# 检查nginx是否运行
if ! pgrep nginx > /dev/null; then
    echo "❌ Nginx 未运行，请先启动 nginx"
    echo "可以使用: docker-compose up -d nginx"
    exit 1
fi

echo "✅ Nginx 正在运行"

# 备份当前配置
echo ""
echo "2️⃣ 备份当前配置..."
BACKUP_DIR="./nginx/conf.d/backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp ./nginx/conf.d/webide-proxy.conf "$BACKUP_DIR/" 2>/dev/null || echo "⚠️ 未找到现有配置文件"
echo "✅ 配置已备份到: $BACKUP_DIR"

# 创建修复后的配置
echo ""
echo "3️⃣ 创建修复后的 nginx 配置..."

cat > ./nginx/conf.d/webide-proxy-fixed.conf << 'EOF'
# OpenVSCode代理配置 - 修复插件页面显示问题
# 解决iframe嵌入限制和安全上下文问题

# 上游OpenVSCode服务器
upstream openvscode_backend {
    server *************:3667;
    keepalive 32;
}

# 代理服务器配置 - HTTP版本（用于开发）
server {
    listen 3667;
    server_name localhost;

    # 增加缓冲区大小处理WebSocket和大文件
    client_max_body_size 100M;
    proxy_buffering off;
    proxy_request_buffering off;

    # 日志配置
    access_log /var/log/nginx/webide-access.log;
    error_log /var/log/nginx/webide-error.log warn;

    # 主要代理配置
    location / {
        # 代理到OpenVSCode服务器
        proxy_pass http://openvscode_backend;
        
        # 基本代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 🔧 修复iframe嵌入问题的关键配置
        # 移除可能阻止iframe嵌入的头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 添加允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *; img-src 'self' data: blob: *; font-src 'self' data: *; connect-src 'self' ws: wss: *; worker-src 'self' blob:;" always;
        
        # 添加安全上下文头，帮助解决crypto.subtle问题
        add_header Cross-Origin-Embedder-Policy "credentialless" always;
        add_header Cross-Origin-Opener-Policy "same-origin" always;
        
        # 🔧 特别针对插件页面的配置
        # 允许所有必要的权限
        add_header Permissions-Policy "camera=*, microphone=*, geolocation=*, clipboard-read=*, clipboard-write=*" always;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 超时配置 - OpenVSCode可能需要较长连接时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
        
        # 禁用缓冲以支持实时通信
        proxy_buffering off;
        proxy_cache off;
        
        # 支持大文件上传
        client_max_body_size 100M;
    }

    # 🔧 特别处理扩展相关的路径
    location ~* /(extensions|marketplace|gallery)/ {
        proxy_pass http://openvscode_backend;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 添加宽松的安全策略
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data: blob:; font-src *; connect-src *; worker-src * blob:;" always;
        
        # 禁用缓存确保最新内容
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    # 静态资源优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://openvscode_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 静态资源缓存
        expires 1h;
        add_header Cache-Control "public, immutable";
        
        # 压缩
        gzip on;
        gzip_types text/css application/javascript application/json;
    }

    # WebSocket特殊处理路径
    location ~* /(websocket|socket\.io|ws)/ {
        proxy_pass http://openvscode_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "OpenVSCode Proxy OK - Extensions Fixed\n";
        add_header Content-Type text/plain;
    }
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}
EOF

echo "✅ 修复配置已创建"

# 应用配置
echo ""
echo "4️⃣ 应用修复配置..."

# 替换配置文件
cp ./nginx/conf.d/webide-proxy-fixed.conf ./nginx/conf.d/webide-proxy.conf

echo "✅ 配置文件已更新"

# 重新加载nginx配置
echo ""
echo "5️⃣ 重新加载 nginx 配置..."

if docker-compose exec nginx nginx -t; then
    echo "✅ 配置文件语法正确"
    docker-compose exec nginx nginx -s reload
    echo "✅ Nginx 配置已重新加载"
else
    echo "❌ 配置文件语法错误，恢复备份"
    cp "$BACKUP_DIR/webide-proxy.conf" ./nginx/conf.d/webide-proxy.conf 2>/dev/null
    echo "⚠️ 已恢复原配置，请检查错误"
    exit 1
fi

echo ""
echo "6️⃣ 验证修复效果..."

# 等待nginx重新加载
sleep 2

# 测试代理是否正常工作
if curl -s --connect-timeout 5 "http://localhost:3668/health" | grep -q "Extensions Fixed"; then
    echo "✅ 代理服务正常，修复配置已生效"
else
    echo "⚠️ 代理服务可能有问题，请检查日志"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "- 移除了 X-Frame-Options 限制"
echo "- 放宽了 Content-Security-Policy 策略"
echo "- 特别处理了扩展相关路径"
echo "- 添加了必要的权限策略"
echo ""
echo "🔄 请现在测试："
echo "1. 刷新前端页面"
echo "2. 检查 OpenVSCode 的插件页面是否正常显示"
echo "3. 尝试安装和使用插件"
echo ""
echo "📞 如果问题仍然存在："
echo "1. 检查浏览器控制台是否还有错误"
echo "2. 尝试清除浏览器缓存"
echo "3. 运行诊断脚本: ./scripts/diagnose-webide-iframe.sh"
