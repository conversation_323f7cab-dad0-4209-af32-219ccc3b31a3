#!/bin/bash

echo "🚀 快速重新构建前端应用"
echo "========================"

echo "📋 当前配置："
echo "- 构建参数: VITE_WEBIDE_BASE_URL=http://192.168.60.52:3669"
echo "- 运行时环境: VITE_WEBIDE_BASE_URL=http://192.168.60.52:3669"
echo "- Token: tk-ynnx-llm"

echo ""
echo "1️⃣ 停止前端应用容器..."
docker-compose stop app

echo ""
echo "2️⃣ 删除旧的应用镜像..."
docker rmi ynnx-aidev-platform-app 2>/dev/null || echo "镜像不存在，跳过删除"

echo ""
echo "3️⃣ 重新构建应用（使用正确的环境变量）..."
docker-compose build --no-cache app

echo ""
echo "4️⃣ 启动应用..."
docker-compose up -d app

echo ""
echo "5️⃣ 等待应用启动..."
sleep 10

echo ""
echo "6️⃣ 检查应用状态..."
docker-compose ps app

echo ""
echo "7️⃣ 检查应用健康状态..."
for i in {1..30}; do
    if curl -s --connect-timeout 2 "http://192.168.60.52:80" > /dev/null; then
        echo "✅ 前端应用已启动并可访问"
        break
    else
        echo "⏳ 等待前端应用启动... ($i/30)"
        sleep 2
    fi
done

echo ""
echo "8️⃣ 检查WebIDE配置..."
echo "访问前端: http://192.168.60.52:80"
echo "进入WebIDE页面，检查是否不再出现403错误"

echo ""
echo "🎉 快速重建完成！"
echo ""
echo "📝 下一步："
echo "1. 访问 http://192.168.60.52:80"
echo "2. 登录后进入WebIDE页面"
echo "3. 检查iframe是否正常加载"
echo "4. 验证插件页面是否显示"

echo ""
echo "🔍 如果仍有问题，请检查："
echo "1. 浏览器控制台错误信息"
echo "2. 应用日志: docker-compose logs app"
echo "3. 确认配置信息显示正确的URL"
