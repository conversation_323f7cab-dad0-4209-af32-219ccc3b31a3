#!/bin/bash
# OpenVSCode服务器启动脚本
# 解决403 Forbidden错误

# 配置变量
OPENVSCODE_PORT=3667
OPENVSCODE_HOST=0.0.0.0
CONNECTION_TOKEN="tk-ynnx-llm"
WORKSPACE_PATH="/tmp/openvscode-workspace"

# 创建工作区目录
mkdir -p "$WORKSPACE_PATH"

echo "🚀 启动OpenVSCode服务器..."
echo "端口: $OPENVSCODE_PORT"
echo "主机: $OPENVSCODE_HOST"
echo "Token: $CONNECTION_TOKEN"
echo "工作区: $WORKSPACE_PATH"

# 方法1: 使用Docker启动 (推荐)
if command -v docker &> /dev/null; then
    echo "使用Docker启动OpenVSCode服务器..."
    docker run -it --rm \
        -p $OPENVSCODE_PORT:3000 \
        -e CONNECTION_TOKEN="$CONNECTION_TOKEN" \
        -e VSCODE_PROXY_URI="http://*************:$OPENVSCODE_PORT" \
        -v "$WORKSPACE_PATH:/home/<USER>" \
        --name openvscode-server \
        gitpod/openvscode-server:latest
else
    echo "❌ Docker未安装，请先安装Docker"
    echo "或者使用本地安装的OpenVSCode服务器"
fi

# 方法2: 使用本地安装的OpenVSCode服务器
# 如果您已经本地安装了OpenVSCode服务器，可以取消注释以下行：
# openvscode-server --host $OPENVSCODE_HOST --port $OPENVSCODE_PORT --connection-token $CONNECTION_TOKEN --folder $WORKSPACE_PATH 