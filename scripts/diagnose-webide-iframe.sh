#!/bin/bash

echo "🔍 OpenVSCode iframe 嵌入问题诊断"
echo "=================================="

# 配置变量
OPENVSCODE_URL="http://*************:3667"
PROXY_URL="http://localhost:3668"
FRONTEND_URL="http://localhost:5173"

echo ""
echo "📋 检查配置信息..."
echo "OpenVSCode URL: $OPENVSCODE_URL"
echo "代理 URL: $PROXY_URL"
echo "前端 URL: $FRONTEND_URL"

# 检查1: OpenVSCode服务器是否运行
echo ""
echo "1️⃣ 检查 OpenVSCode 服务器状态..."
if curl -s --connect-timeout 5 "$OPENVSCODE_URL" > /dev/null; then
    echo "✅ OpenVSCode 服务器正在运行"
else
    echo "❌ OpenVSCode 服务器未运行或无法访问"
    echo "请先启动 OpenVSCode 服务器"
    exit 1
fi

# 检查2: 检查安全头部
echo ""
echo "2️⃣ 检查安全头部配置..."
echo "检查 X-Frame-Options..."
FRAME_OPTIONS=$(curl -s -I "$OPENVSCODE_URL" | grep -i "x-frame-options" || echo "未设置")
echo "X-Frame-Options: $FRAME_OPTIONS"

echo "检查 Content-Security-Policy..."
CSP=$(curl -s -I "$OPENVSCODE_URL" | grep -i "content-security-policy" || echo "未设置")
echo "Content-Security-Policy: $CSP"

# 检查3: 检查插件页面特定的响应
echo ""
echo "3️⃣ 检查插件页面响应..."
EXTENSIONS_URL="$OPENVSCODE_URL/static/extensions"
echo "检查扩展页面: $EXTENSIONS_URL"
EXTENSIONS_RESPONSE=$(curl -s -I "$EXTENSIONS_URL" | head -1)
echo "扩展页面响应: $EXTENSIONS_RESPONSE"

# 检查4: 检查代理配置
echo ""
echo "4️⃣ 检查代理配置..."
if curl -s --connect-timeout 5 "$PROXY_URL" > /dev/null; then
    echo "✅ 代理服务器正在运行"
    
    echo "检查代理的安全头部..."
    PROXY_FRAME_OPTIONS=$(curl -s -I "$PROXY_URL" | grep -i "x-frame-options" || echo "未设置")
    echo "代理 X-Frame-Options: $PROXY_FRAME_OPTIONS"
    
    PROXY_CSP=$(curl -s -I "$PROXY_URL" | grep -i "content-security-policy" || echo "未设置")
    echo "代理 Content-Security-Policy: $PROXY_CSP"
else
    echo "❌ 代理服务器未运行"
    echo "请检查 nginx 配置和启动状态"
fi

# 检查5: 测试iframe嵌入
echo ""
echo "5️⃣ 生成测试页面..."
cat > /tmp/iframe-test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>OpenVSCode iframe 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>OpenVSCode iframe 嵌入测试</h1>
    
    <div class="test-section">
        <h2>测试1: 直接嵌入 OpenVSCode</h2>
        <iframe src="http://*************:3667?tkn=tk-ynnx-llm" 
                title="OpenVSCode Direct"></iframe>
        <p id="direct-status">加载中...</p>
    </div>
    
    <div class="test-section">
        <h2>测试2: 通过代理嵌入</h2>
        <iframe src="http://localhost:3668?tkn=tk-ynnx-llm" 
                title="OpenVSCode Proxy"></iframe>
        <p id="proxy-status">加载中...</p>
    </div>
    
    <script>
        // 监听iframe加载事件
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, index) => {
            iframe.onload = function() {
                const statusId = index === 0 ? 'direct-status' : 'proxy-status';
                const statusEl = document.getElementById(statusId);
                
                try {
                    // 尝试访问iframe内容
                    const doc = iframe.contentDocument;
                    if (doc) {
                        statusEl.innerHTML = '<span class="success">✅ 加载成功，可以访问内容</span>';
                    } else {
                        statusEl.innerHTML = '<span class="error">⚠️ 加载成功，但无法访问内容（跨域限制）</span>';
                    }
                } catch (e) {
                    statusEl.innerHTML = '<span class="error">⚠️ 加载成功，但无法访问内容（跨域限制）</span>';
                }
            };
            
            iframe.onerror = function() {
                const statusId = index === 0 ? 'direct-status' : 'proxy-status';
                const statusEl = document.getElementById(statusId);
                statusEl.innerHTML = '<span class="error">❌ 加载失败</span>';
            };
        });
        
        // 检查控制台错误
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event);
        });
    </script>
</body>
</html>
EOF

echo "✅ 测试页面已生成: /tmp/iframe-test.html"
echo ""
echo "📖 下一步操作："
echo "1. 在浏览器中打开: file:///tmp/iframe-test.html"
echo "2. 检查两个iframe的加载情况"
echo "3. 打开浏览器开发者工具查看控制台错误"
echo "4. 特别注意是否有 X-Frame-Options 或 CSP 相关错误"

echo ""
echo "🔧 常见问题和解决方案："
echo "- 如果看到 'X-Frame-Options: DENY' 错误，需要修改 nginx 配置"
echo "- 如果看到 CSP 错误，需要添加 frame-ancestors 指令"
echo "- 如果插件页面特别不显示，可能需要特殊的权限配置"

echo ""
echo "📞 如需进一步帮助，请提供："
echo "1. 浏览器控制台的具体错误信息"
echo "2. 测试页面中两个iframe的显示情况"
echo "3. 是否只有插件页面不显示，还是整个IDE都有问题"
