#!/bin/bash

echo "🧪 测试 OpenVSCode 插件页面显示"
echo "================================"

# 配置变量
OPENVSCODE_URL="http://*************:3667"
PROXY_URL="http://localhost:3668"

echo ""
echo "📋 测试配置："
echo "OpenVSCode URL: $OPENVSCODE_URL"
echo "代理 URL: $PROXY_URL"

# 测试1: 检查主页面
echo ""
echo "1️⃣ 测试主页面访问..."
MAIN_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$OPENVSCODE_URL?tkn=tk-ynnx-llm")
echo "主页面响应码: $MAIN_RESPONSE"

if [ "$MAIN_RESPONSE" = "200" ] || [ "$MAIN_RESPONSE" = "302" ]; then
    echo "✅ 主页面访问正常"
else
    echo "❌ 主页面访问异常"
fi

# 测试2: 检查代理页面
echo ""
echo "2️⃣ 测试代理页面访问..."
PROXY_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$PROXY_URL?tkn=tk-ynnx-llm")
echo "代理页面响应码: $PROXY_RESPONSE"

if [ "$PROXY_RESPONSE" = "200" ] || [ "$PROXY_RESPONSE" = "302" ]; then
    echo "✅ 代理页面访问正常"
else
    echo "❌ 代理页面访问异常"
fi

# 测试3: 检查安全头部
echo ""
echo "3️⃣ 检查安全头部..."
echo "检查代理的 X-Frame-Options..."
FRAME_OPTIONS=$(curl -s -I "$PROXY_URL" | grep -i "x-frame-options" | head -1)
if [ -n "$FRAME_OPTIONS" ]; then
    echo "X-Frame-Options: $FRAME_OPTIONS"
    if echo "$FRAME_OPTIONS" | grep -qi "ALLOWALL\|SAMEORIGIN"; then
        echo "✅ X-Frame-Options 配置正确"
    else
        echo "⚠️ X-Frame-Options 可能阻止iframe嵌入"
    fi
else
    echo "⚠️ 未检测到 X-Frame-Options 头部"
fi

# 创建测试页面
echo ""
echo "4️⃣ 创建插件页面测试页面..."

cat > /tmp/extensions-test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>OpenVSCode 插件页面测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #1e1e1e; 
            color: #fff; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        iframe { 
            width: 100%; 
            height: 800px; 
            border: 2px solid #333; 
            border-radius: 8px;
            background: #fff;
        }
        .test-info { 
            background: #2d2d2d; 
            padding: 15px; 
            border-radius: 8px; 
            margin: 20px 0; 
        }
        .status { 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .success { background: #0d7377; }
        .error { background: #d32f2f; }
        .warning { background: #f57c00; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a9e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OpenVSCode 插件页面测试</h1>
        
        <div class="test-info">
            <h2>测试说明</h2>
            <p>此页面用于测试 OpenVSCode 在 iframe 中的插件页面显示情况</p>
            <p>如果插件页面正常显示，说明 iframe 嵌入配置正确</p>
        </div>
        
        <div class="test-info">
            <h2>控制面板</h2>
            <button onclick="loadDirect()">加载直接连接</button>
            <button onclick="loadProxy()">加载代理连接</button>
            <button onclick="openInNewTab()">在新标签页打开</button>
            <button onclick="checkConsole()">检查控制台</button>
        </div>
        
        <div id="status" class="status warning">
            等待加载...
        </div>
        
        <iframe id="vscode-iframe" 
                title="OpenVSCode"
                allow="fullscreen; clipboard-read; clipboard-write; cross-origin-isolated; camera; microphone; geolocation; autoplay; encrypted-media; picture-in-picture"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-top-navigation allow-top-navigation-by-user-activation allow-downloads allow-modals">
        </iframe>
        
        <div class="test-info">
            <h2>测试结果</h2>
            <div id="test-results">
                <p>等待测试...</p>
            </div>
        </div>
    </div>
    
    <script>
        const iframe = document.getElementById('vscode-iframe');
        const status = document.getElementById('status');
        const results = document.getElementById('test-results');
        
        let testResults = [];
        
        function updateStatus(message, type = 'warning') {
            status.className = `status ${type}`;
            status.textContent = message;
        }
        
        function addResult(test, result, details = '') {
            testResults.push({ test, result, details });
            updateResults();
        }
        
        function updateResults() {
            results.innerHTML = testResults.map(r => 
                `<p><strong>${r.test}:</strong> ${r.result} ${r.details}</p>`
            ).join('');
        }
        
        function loadDirect() {
            updateStatus('加载直接连接...', 'warning');
            iframe.src = 'http://*************:3667?tkn=tk-ynnx-llm';
            addResult('直接连接', '尝试加载中...');
        }
        
        function loadProxy() {
            updateStatus('加载代理连接...', 'warning');
            iframe.src = 'http://localhost:3668?tkn=tk-ynnx-llm';
            addResult('代理连接', '尝试加载中...');
        }
        
        function openInNewTab() {
            const url = iframe.src || 'http://localhost:3668?tkn=tk-ynnx-llm';
            window.open(url, '_blank');
            addResult('新标签页', '已打开，请检查插件页面是否正常');
        }
        
        function checkConsole() {
            addResult('控制台检查', '请打开开发者工具查看控制台错误信息');
        }
        
        // 监听iframe事件
        iframe.onload = function() {
            updateStatus('iframe 加载完成', 'success');
            
            try {
                const doc = iframe.contentDocument;
                if (doc) {
                    addResult('内容访问', '✅ 可以访问iframe内容');
                    
                    // 检查是否是VSCode页面
                    if (doc.title.includes('VSCode') || doc.body.innerHTML.includes('vscode')) {
                        addResult('VSCode检测', '✅ 检测到VSCode内容');
                    } else {
                        addResult('VSCode检测', '⚠️ 未检测到VSCode特征');
                    }
                } else {
                    addResult('内容访问', '⚠️ 无法访问iframe内容（跨域限制）');
                }
            } catch (e) {
                addResult('内容访问', '⚠️ 无法访问iframe内容（跨域限制）');
                console.log('iframe访问错误:', e);
            }
        };
        
        iframe.onerror = function(e) {
            updateStatus('iframe 加载失败', 'error');
            addResult('加载状态', '❌ iframe加载失败');
            console.error('iframe错误:', e);
        };
        
        // 监听消息
        window.addEventListener('message', function(event) {
            console.log('收到iframe消息:', event);
            addResult('消息通信', `收到来自 ${event.origin} 的消息`);
        });
        
        // 监听控制台错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
            addResult('页面错误', `${e.message} (${e.filename}:${e.lineno})`);
        });
        
        // 自动开始测试
        setTimeout(() => {
            loadProxy();
        }, 1000);
    </script>
</body>
</html>
EOF

echo "✅ 测试页面已创建: /tmp/extensions-test.html"

echo ""
echo "🚀 开始测试步骤："
echo "1. 运行修复脚本: ./scripts/fix-webide-extensions.sh"
echo "2. 在浏览器中打开: file:///tmp/extensions-test.html"
echo "3. 观察iframe是否能正常加载OpenVSCode"
echo "4. 特别检查插件页面（Extensions）是否显示"

echo ""
echo "🔍 检查要点："
echo "- iframe是否能加载OpenVSCode界面"
echo "- 左侧边栏的插件图标是否可点击"
echo "- 插件页面是否显示插件列表"
echo "- 是否能搜索和安装插件"

echo ""
echo "📞 如果问题仍然存在，请提供："
echo "1. 浏览器控制台的具体错误信息"
echo "2. 插件页面是完全不显示还是显示空白"
echo "3. 其他功能（如文件浏览器、编辑器）是否正常"
