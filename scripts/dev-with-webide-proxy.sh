#!/bin/bash

echo "🔧 启动开发环境 (包含WebIDE代理)"
echo "======================================="

# 检查端口占用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用 ($service)"
        echo "   占用进程: $(lsof -Pi :$port -sTCP:LISTEN | tail -1)"
        return 1
    else
        echo "✅ 端口 $port 可用 ($service)"
        return 0
    fi
}

# 检查必要端口
echo "🔍 检查端口占用情况..."
check_port 5173 "Vite开发服务器"
check_port 5174 "Vite备用端口"
check_port 3667 "WebIDE代理"

# 检查OpenVSCode服务器
echo ""
echo "🔍 检查OpenVSCode服务器..."
if curl -s --connect-timeout 3 "http://*************:3667" > /dev/null; then
    echo "✅ OpenVSCode服务器运行正常 (*************:3667)"
else
    echo "❌ OpenVSCode服务器未响应 (*************:3667)"
    echo "   请先启动OpenVSCode服务器"
    echo "   快速启动: docker run -p 3667:3000 -e CONNECTION_TOKEN=tk-ynnx-llm gitpod/openvscode-server"
    exit 1
fi

# 检查docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，无法启动nginx代理"
    echo "   请安装Docker或使用scripts/setup-localhost-proxy.sh"
    exit 1
fi

# 启动nginx代理容器
echo ""
echo "🚀 启动nginx代理容器..."

# 停止可能存在的代理容器
docker stop ynnx-webide-proxy 2>/dev/null || true
docker rm ynnx-webide-proxy 2>/dev/null || true

# 创建临时nginx配置
TEMP_NGINX_CONF="/tmp/dev-webide-proxy.conf"
cat > "$TEMP_NGINX_CONF" << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream openvscode_backend {
        server *************:3667;
        keepalive 32;
    }

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    server {
        listen 3667;
        server_name localhost;

        location / {
            proxy_pass http://openvscode_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;

            # 超时配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_buffering off;
        }

        location /health {
            access_log off;
            return 200 "DEV WebIDE Proxy OK\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

# 启动nginx代理容器
docker run -d \
    --name ynnx-webide-proxy \
    --restart unless-stopped \
    -p 3667:3667 \
    -v "$TEMP_NGINX_CONF:/etc/nginx/nginx.conf:ro" \
    nginx:alpine

if [ $? -eq 0 ]; then
    echo "✅ nginx代理容器启动成功"
else
    echo "❌ nginx代理容器启动失败"
    exit 1
fi

# 等待代理启动
echo "⏳ 等待代理启动..."
sleep 3

# 测试代理
if curl -s --connect-timeout 5 "http://localhost:3667/health" > /dev/null; then
    echo "✅ WebIDE代理正常工作"
else
    echo "❌ WebIDE代理异常"
    docker logs ynnx-webide-proxy
    exit 1
fi

# 更新环境变量（如果存在.env文件）
echo ""
echo "⚙️  更新环境配置..."
if [ -f ".env" ]; then
    sed -i 's|VITE_WEBIDE_BASE_URL=.*|VITE_WEBIDE_BASE_URL=http://localhost:3667|' .env
    echo "✅ .env文件已更新为使用localhost代理"
else
    echo "📝 .env文件不存在，从env.example创建..."
    cp env.example .env
    sed -i 's|VITE_WEBIDE_BASE_URL=.*|VITE_WEBIDE_BASE_URL=http://localhost:3667|' .env
fi

# 设置临时环境变量
export VITE_WEBIDE_BASE_URL=http://localhost:3667

echo ""
echo "🚀 启动Vite开发服务器..."
echo "   环境变量: VITE_WEBIDE_BASE_URL=http://localhost:3667"
echo ""

# 启动vite开发服务器
npm run dev &
VITE_PID=$!

echo ""
echo "✨ 开发环境启动完成!"
echo ""
echo "🌐 访问地址:"
echo "  - 开发服务器: http://localhost:5173 或 http://localhost:5174"
echo "  - WebIDE代理: http://localhost:3667"
echo "  - 代理健康检查: http://localhost:3667/health"
echo ""
echo "📋 管理命令:"
echo "  - 停止代理: docker stop ynnx-webide-proxy"
echo "  - 查看代理日志: docker logs ynnx-webide-proxy"
echo "  - 重启代理: docker restart ynnx-webide-proxy"
echo ""
echo "⚠️  退出时请手动停止代理容器:"
echo "   docker stop ynnx-webide-proxy && docker rm ynnx-webide-proxy"

# 等待用户退出
echo ""
echo "按 Ctrl+C 退出开发服务器..."
wait $VITE_PID 