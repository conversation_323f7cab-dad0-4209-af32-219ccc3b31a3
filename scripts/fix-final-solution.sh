#!/bin/bash

echo "🔧 最终修复方案 - 解决前端WebIDE 403错误"
echo "=========================================="

echo "🔍 问题分析："
echo "1. Docker构建时的环境变量配置错误"
echo "2. 前端应用在浏览器中运行，需要使用宿主机IP"
echo "3. 环境变量配置不一致"

# 检查当前状态
echo ""
echo "1️⃣ 检查当前状态..."
echo "OpenVSCode直接访问: http://*************:3667/?tkn=tk-ynnx-llm"
echo "Nginx代理访问: http://*************:3669/?tkn=tk-ynnx-llm"
echo "前端访问: http://*************:80"

# 备份配置
echo ""
echo "2️⃣ 备份当前配置..."
BACKUP_DIR="./backup-final-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp .env "$BACKUP_DIR/"
cp docker-compose.yml "$BACKUP_DIR/"
echo "✅ 配置已备份到: $BACKUP_DIR"

# 修复docker-compose.yml中的构建参数
echo ""
echo "3️⃣ 修复Docker构建配置..."

# 更新docker-compose.yml中的build args
sed -i 's|VITE_WEBIDE_BASE_URL: http://localhost:3668|VITE_WEBIDE_BASE_URL: http://*************:3669|g' docker-compose.yml

echo "✅ Docker构建参数已更新"

# 确保环境变量配置正确
echo ""
echo "4️⃣ 确保环境变量配置正确..."

# 更新.env文件
cat > .env << 'EOF'
# ===================================================================
# YNNX AI Platform - 环境变量配置
# 复制此文件为 .env 并根据实际环境修改相应的值
# ===================================================================

# ===== 应用基础配置 =====
NODE_ENV=development

# 跨域配置
CORS_ORIGIN=*

# ===== 服务端口配置 =====

# LDAP认证服务配置
LDAP_AUTH_PORT=3002

# ===== 前端API配置 =====
# LDAP认证API服务器
VITE_LDAP_API_URL=http://*************:3002

# LiteLLM前端API
VITE_LITELLM_API_BASE=http://*************:14000

# ===== LDAP 认证配置 =====
# LDAP基础配置
LDAP_DEFAULT_HOST=*************
LDAP_DEFAULT_PORT=11389
LDAP_DEFAULT_ENVIRONMENT=DEVVDI_ENV

# LDAP连接配置
LDAP_CONNECTION_TIMEOUT=5000
LDAP_SEARCH_TIMEOUT=10000
LDAP_MAX_RETRIES=3

# LDAP测试端口配置
LDAP_TEST_PORT=11389
LDAPS_TEST_PORT=636
LDAP_GC_PORT=3268
LDAPS_GC_PORT=3269

# ===== LDAP 环境配置 =====
# 240.10云桌面环境 (DEVVDI)
LDAP_DEVVDI_ENV_URL=ldap://*************:11389
LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
LDAP_DEVVDI_ENV_BIND_DN=
LDAP_DEVVDI_ENV_BIND_PASSWORD=
LDAP_DEVVDI_ENV_USER_SEARCH_BASE=DC=DEVVDI,DC=YNRCC,DC=COM
LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
LDAP_DEVVDI_ENV_USER_DOMAIN=@DEVVDI.YNRCC.COM
LDAP_DEVVDI_ENV_USE_DIRECT_BIND=true
LDAP_DEVVDI_ENV_NAME=240.10云桌面环境
LDAP_DEVVDI_ENV_DESCRIPTION=DEVVDI Active Directory环境 - 端口389

# 242.2云桌面环境 (VDI)
LDAP_VDI_ENV_URL=ldap://*************:12389
LDAP_VDI_ENV_BASE_DN=DC=VDI,DC=YNNX,DC=COM
LDAP_VDI_ENV_BIND_DN=
LDAP_VDI_ENV_BIND_PASSWORD=
LDAP_VDI_ENV_USER_SEARCH_BASE=DC=VDI,DC=YNNX,DC=COM
LDAP_VDI_ENV_USER_FILTER=(userPrincipalName={{username}}@VDI.YNNX.COM)
LDAP_VDI_ENV_USER_DN_PATTERN={{username}}@VDI.YNNX.COM
LDAP_VDI_ENV_USER_DOMAIN=@VDI.YNNX.COM
LDAP_VDI_ENV_USE_DIRECT_BIND=true
LDAP_VDI_ENV_NAME=242.2云桌面环境
LDAP_VDI_ENV_DESCRIPTION=VDI Active Directory环境 - 端口389

# ===== LiteLLM 代理服务配置 =====
LITELLM_API_BASE=http://*************:14000
LITELLM_MASTER_KEY=sk-ynnx-llm-20250530

# ===== LLM 模型配置 =====
# OpenAI配置
OPENAI_API_KEY=sk-ynnx-llm-20250530
OPENAI_MODEL=qwen3-235b-a22b
OPENAI_BASE_URL=http://*************:14000/v1

# Anthropic Claude配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Azure OpenAI配置
AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com
AZURE_OPENAI_API_KEY=your_azure_api_key_here

# ===== LLM 功能开关 =====
ENABLE_OPENAI=true
ENABLE_ANTHROPIC=false
ENABLE_AZURE=false

# ===== 性能配置 =====
# LLM调用配置
LLM_TIMEOUT=90000              # LLM API超时时间(毫秒) - 30秒
LLM_MAX_TOKENS=1000           # 最大生成token数
LLM_TEMPERATURE=0.7           # 生成文本的随机性

# API重试配置
API_RETRY_COUNT=1              # API重试次数
API_RETRY_DELAY=1000           # API重试延迟(毫秒)

# env for doc
DOC_LLM_API_URL = http://*************:14000/v1
DOC_LLM_MODEL = qwen3-235b-a22b

# LiteLLM API配置
LITELLM_PORT=4000
LITELLM_HOST=*************
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
DEEPSEEK_API_KEY=your-deepseek-key

# 数据库配置
DATABASE_URL=sqlite:///var/lib/litellm/db/litellm.db

# LDAP认证服务配置
LDAP_HOST=*************
LDAP_USER_BASE=ou=Users,dc=ynnx,dc=com
LDAP_BIND_DN=cn=admin,dc=ynnx,dc=com
LDAP_BIND_PASSWORD=your-ldap-password

# 前端API配置
VITE_LITELLM_API_BASE=http://*************:14000
VITE_LDAP_API_URL=http://*************:3002

# 安全配置
JWT_SECRET=your-very-secure-jwt-secret-key
SESSION_SECRET=your-session-secret-key

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/ynnx-ai

# ===================================================================
# 部署说明:
# 1. 复制此文件为 .env: cp env.example .env
# 2. 根据实际环境修改相应的配置值
# 3. LDAP配置完全通过环境变量管理，无需额外的JSON配置文件
# 4. 生产环境请配置真实的API密钥
# ===================================================================

# Web IDE配置 - 修正为实际的网络架构
VITE_WEBIDE_BASE_URL=http://*************:3669
VITE_WEBIDE_TOKEN=tk-ynnx-llm
VITE_WEBIDE_TOKEN_PARAM=tkn
EOF

echo "✅ 环境变量配置已更新"

# 重新构建和启动服务
echo ""
echo "5️⃣ 重新构建和启动服务..."

echo "停止现有服务..."
docker-compose down

echo "清理旧镜像..."
docker rmi ynnx-aidev-platform-app 2>/dev/null || true

echo "重新构建应用..."
docker-compose build --no-cache app

echo "启动所有服务..."
docker-compose up -d

echo "等待服务启动..."
sleep 20

# 验证修复效果
echo ""
echo "6️⃣ 验证修复效果..."

echo "检查服务状态..."
docker-compose ps

echo "检查前端应用..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:80")
echo "前端应用响应码: $FRONTEND_STATUS"

echo "检查nginx代理..."
PROXY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:3669/?tkn=tk-ynnx-llm")
echo "代理响应码: $PROXY_STATUS"

echo "检查代理健康状态..."
if curl -s "http://*************:3669/health" | grep -q "Extensions Fixed"; then
    echo "✅ 代理配置正常"
else
    echo "⚠️ 代理配置可能有问题"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "- 修正了Docker构建时的环境变量配置"
echo "- 统一了所有配置文件中的WebIDE URL"
echo "- 重新构建了前端应用"
echo "- 确保nginx代理配置正确"
echo ""
echo "🔄 请现在测试："
echo "1. 访问前端: http://*************:80"
echo "2. 登录后进入WebIDE页面"
echo "3. 检查是否不再出现403错误"
echo "4. 验证插件页面是否正常显示"
echo ""
echo "🔍 如果仍有问题："
echo "1. 检查浏览器控制台错误"
echo "2. 检查应用日志: docker-compose logs app"
echo "3. 检查nginx日志: docker-compose logs nginx"
echo "4. 确认OpenVSCode服务器正在运行"
