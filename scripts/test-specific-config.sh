#!/bin/bash

echo "🧪 测试具体配置的 OpenVSCode 插件页面"
echo "======================================="

echo "📋 测试目标："
echo "- 前端: http://*************:80"
echo "- OpenVSCode直接: http://*************:3667/?tkn=tk-ynnx-llm"
echo "- Nginx代理: http://*************:3669/?tkn=tk-ynnx-llm"

# 测试1: 检查所有服务状态
echo ""
echo "1️⃣ 检查服务状态..."

echo "检查 OpenVSCode 服务器..."
OPENVSCODE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:3667")
echo "OpenVSCode 响应码: $OPENVSCODE_STATUS"

echo "检查 Nginx 代理..."
PROXY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:3669")
echo "代理 响应码: $PROXY_STATUS"

echo "检查前端应用..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:80")
echo "前端 响应码: $FRONTEND_STATUS"

# 测试2: 检查安全头部
echo ""
echo "2️⃣ 检查安全头部..."

echo "检查代理的安全头部..."
HEADERS=$(curl -s -I "http://*************:3669" 2>/dev/null)

echo "X-Frame-Options:"
echo "$HEADERS" | grep -i "x-frame-options" || echo "  未设置"

echo "Content-Security-Policy:"
echo "$HEADERS" | grep -i "content-security-policy" || echo "  未设置"

echo "Access-Control-Allow-Origin:"
echo "$HEADERS" | grep -i "access-control-allow-origin" || echo "  未设置"

# 测试3: 创建专门的测试页面
echo ""
echo "3️⃣ 创建测试页面..."

cat > /tmp/specific-config-test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>OpenVSCode 插件页面测试 - 具体配置</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #1e1e1e; 
            color: #fff; 
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-panel {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 15px;
        }
        iframe { 
            width: 100%; 
            height: 600px; 
            border: 2px solid #333; 
            border-radius: 8px;
            background: #fff;
        }
        .status { 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0; 
        }
        .success { background: #0d7377; }
        .error { background: #d32f2f; }
        .warning { background: #f57c00; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        button:hover { background: #005a9e; }
        .info { font-size: 12px; color: #ccc; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OpenVSCode 插件页面测试 - 具体配置</h1>
        
        <div class="test-panel">
            <h2>网络架构</h2>
            <div class="info">
                <p>前端: http://*************:80</p>
                <p>OpenVSCode直接: http://*************:3667/?tkn=tk-ynnx-llm</p>
                <p>Nginx代理: http://*************:3669/?tkn=tk-ynnx-llm</p>
            </div>
        </div>
        
        <div class="test-grid">
            <div class="test-panel">
                <h3>🎯 代理访问测试</h3>
                <button onclick="loadProxy()">加载代理</button>
                <button onclick="openProxy()">新窗口打开</button>
                <div id="proxy-status" class="status warning">等待测试...</div>
                <iframe id="proxy-iframe" 
                        title="OpenVSCode Proxy"
                        allow="fullscreen; clipboard-read; clipboard-write; cross-origin-isolated; camera; microphone; geolocation; autoplay; encrypted-media; picture-in-picture"
                        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-top-navigation allow-top-navigation-by-user-activation allow-downloads allow-modals">
                </iframe>
            </div>
            
            <div class="test-panel">
                <h3>🔗 直接访问测试</h3>
                <button onclick="loadDirect()">加载直接</button>
                <button onclick="openDirect()">新窗口打开</button>
                <div id="direct-status" class="status warning">等待测试...</div>
                <iframe id="direct-iframe" 
                        title="OpenVSCode Direct"
                        allow="fullscreen; clipboard-read; clipboard-write; cross-origin-isolated; camera; microphone; geolocation; autoplay; encrypted-media; picture-in-picture"
                        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-top-navigation allow-top-navigation-by-user-activation allow-downloads allow-modals">
                </iframe>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>🔍 测试结果</h2>
            <div id="test-results">
                <p>等待测试...</p>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>📝 测试说明</h2>
            <div class="info">
                <p><strong>重点检查：</strong></p>
                <ul>
                    <li>两个iframe是否都能正常加载OpenVSCode界面</li>
                    <li>左侧边栏的插件图标（四个方块）是否可点击</li>
                    <li>点击插件图标后是否显示插件列表</li>
                    <li>是否能搜索插件（如搜索"Python"）</li>
                    <li>插件详情页面是否正常显示</li>
                </ul>
                <p><strong>如果代理访问正常但前端嵌入有问题：</strong></p>
                <ul>
                    <li>检查前端的iframe配置</li>
                    <li>检查浏览器控制台错误</li>
                    <li>确认环境变量配置正确</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        const proxyIframe = document.getElementById('proxy-iframe');
        const directIframe = document.getElementById('direct-iframe');
        const proxyStatus = document.getElementById('proxy-status');
        const directStatus = document.getElementById('direct-status');
        const results = document.getElementById('test-results');
        
        let testResults = [];
        
        function updateStatus(element, message, type = 'warning') {
            element.className = `status ${type}`;
            element.textContent = message;
        }
        
        function addResult(test, result, details = '') {
            testResults.push({ test, result, details, time: new Date().toLocaleTimeString() });
            updateResults();
        }
        
        function updateResults() {
            results.innerHTML = testResults.map(r => 
                `<p><strong>[${r.time}] ${r.test}:</strong> ${r.result} ${r.details}</p>`
            ).join('');
        }
        
        function loadProxy() {
            updateStatus(proxyStatus, '加载代理中...', 'warning');
            proxyIframe.src = 'http://*************:3669/?tkn=tk-ynnx-llm';
            addResult('代理加载', '开始加载...');
        }
        
        function loadDirect() {
            updateStatus(directStatus, '加载直接连接中...', 'warning');
            directIframe.src = 'http://*************:3667/?tkn=tk-ynnx-llm';
            addResult('直接加载', '开始加载...');
        }
        
        function openProxy() {
            window.open('http://*************:3669/?tkn=tk-ynnx-llm', '_blank');
            addResult('代理新窗口', '已在新窗口打开，请检查插件页面');
        }
        
        function openDirect() {
            window.open('http://*************:3667/?tkn=tk-ynnx-llm', '_blank');
            addResult('直接新窗口', '已在新窗口打开，请检查插件页面');
        }
        
        // 监听iframe事件
        proxyIframe.onload = function() {
            updateStatus(proxyStatus, '代理iframe加载完成', 'success');
            addResult('代理iframe', '✅ 加载完成');
        };
        
        proxyIframe.onerror = function(e) {
            updateStatus(proxyStatus, '代理iframe加载失败', 'error');
            addResult('代理iframe', '❌ 加载失败');
        };
        
        directIframe.onload = function() {
            updateStatus(directStatus, '直接iframe加载完成', 'success');
            addResult('直接iframe', '✅ 加载完成');
        };
        
        directIframe.onerror = function(e) {
            updateStatus(directStatus, '直接iframe加载失败', 'error');
            addResult('直接iframe', '❌ 加载失败');
        };
        
        // 监听控制台错误
        window.addEventListener('error', function(e) {
            addResult('页面错误', `${e.message} (${e.filename}:${e.lineno})`);
        });
        
        // 自动开始测试
        setTimeout(() => {
            addResult('自动测试', '开始自动加载代理访问...');
            loadProxy();
        }, 1000);
    </script>
</body>
</html>
EOF

echo "✅ 测试页面已创建: /tmp/specific-config-test.html"

# 测试4: 检查token参数
echo ""
echo "4️⃣ 检查token参数..."

echo "测试带token的访问..."
TOKEN_TEST=$(curl -s -o /dev/null -w "%{http_code}" "http://*************:3669/?tkn=tk-ynnx-llm")
echo "带token的代理访问响应码: $TOKEN_TEST"

if [ "$TOKEN_TEST" = "200" ] || [ "$TOKEN_TEST" = "302" ]; then
    echo "✅ Token认证正常"
else
    echo "⚠️ Token认证可能有问题"
fi

echo ""
echo "🚀 测试步骤："
echo "1. 在浏览器中打开: file:///tmp/specific-config-test.html"
echo "2. 观察两个iframe的加载情况"
echo "3. 特别注意代理访问的iframe是否正常"
echo "4. 点击'新窗口打开'按钮对比效果"
echo "5. 在新窗口中测试插件页面功能"

echo ""
echo "🔍 重点检查："
echo "- 代理访问(3669端口)是否正常显示OpenVSCode"
echo "- 插件页面(Extensions)是否可以正常显示"
echo "- 插件搜索功能是否正常"
echo "- 与直接访问(3667端口)的效果对比"

echo ""
echo "📞 如果仍有问题，请提供："
echo "1. 测试页面中两个iframe的显示情况"
echo "2. 浏览器控制台的具体错误信息"
echo "3. 新窗口打开时插件页面的表现"
echo "4. 前端页面(http://*************:80)中WebIDE的表现"
