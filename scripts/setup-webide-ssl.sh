#!/bin/bash

# 设置WebIDE SSL证书
# 在Docker容器环境中运行

set -e

echo "🔐 设置WebIDE SSL证书..."

# 创建SSL目录
mkdir -p nginx/ssl

# 生成证书的Docker命令
echo "📝 在临时容器中生成SSL证书..."

# 使用alpine容器生成证书
docker run --rm \
  -v "$(pwd)/nginx/ssl:/certs" \
  alpine:latest \
  sh -c "
    # 安装openssl
    apk add --no-cache openssl
    
    # 生成私钥
    openssl genrsa -out /certs/webide.key 2048
    
    # 生成自签名证书
    openssl req -new -x509 -key /certs/webide.key -out /certs/webide.crt -days 365 \
      -subj '/C=CN/ST=Yunnan/L=Kunming/O=YNNX/OU=AI Platform/CN=localhost'
    
    # 设置权限
    chmod 600 /certs/webide.key
    chmod 644 /certs/webide.crt
    
    echo '✅ SSL证书生成完成！'
    echo '📁 私钥: webide.key'
    echo '📜 证书: webide.crt'
  "

echo ""
echo "✅ WebIDE SSL设置完成！"
echo "🔗 HTTPS访问地址: https://localhost:3668"
echo "⚠️  注意: 这是自签名证书，浏览器会显示安全警告"
echo "💡 使用时请点击 '高级' -> '继续访问localhost(不安全)'"