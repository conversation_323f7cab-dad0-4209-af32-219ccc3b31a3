#!/bin/bash

echo "🔧 修复 OpenVSCode 插件页面显示问题 - 针对具体配置"
echo "=================================================="

echo "📋 当前网络架构："
echo "- 前端访问: http://*************:80"
echo "- OpenVSCode直接: http://*************:3667/?tkn=tk-ynnx-llm"
echo "- Nginx代理: http://*************:3669"

# 检查当前状态
echo ""
echo "1️⃣ 检查当前服务状态..."

# 检查OpenVSCode服务器
if curl -s --connect-timeout 5 "http://*************:3667" > /dev/null; then
    echo "✅ OpenVSCode 服务器正在运行"
else
    echo "❌ OpenVSCode 服务器未运行，请先启动"
    exit 1
fi

# 检查nginx代理
if curl -s --connect-timeout 5 "http://*************:3669" > /dev/null; then
    echo "✅ Nginx 代理正在运行"
else
    echo "❌ Nginx 代理未运行，请检查 docker-compose"
    exit 1
fi

# 检查前端
if curl -s --connect-timeout 5 "http://*************:80" > /dev/null; then
    echo "✅ 前端应用正在运行"
else
    echo "❌ 前端应用未运行，请检查 docker-compose"
    exit 1
fi

# 备份配置
echo ""
echo "2️⃣ 备份当前配置..."
BACKUP_DIR="./backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp .env "$BACKUP_DIR/" 2>/dev/null
cp ./nginx/conf.d/webide-proxy.conf "$BACKUP_DIR/" 2>/dev/null
echo "✅ 配置已备份到: $BACKUP_DIR"

# 修正环境变量配置
echo ""
echo "3️⃣ 修正环境变量配置..."

# 更新.env文件中的WebIDE配置
sed -i 's|VITE_WEBIDE_BASE_URL=.*|VITE_WEBIDE_BASE_URL=http://*************:3669|g' .env
sed -i 's|VITE_WEBIDE_TOKEN_PARAM=.*|VITE_WEBIDE_TOKEN_PARAM=tkn|g' .env

echo "✅ 环境变量已更新"
echo "- VITE_WEBIDE_BASE_URL=http://*************:3669"
echo "- VITE_WEBIDE_TOKEN_PARAM=tkn"

# 创建修复后的nginx配置
echo ""
echo "4️⃣ 创建修复后的 nginx 配置..."

cat > ./nginx/conf.d/webide-proxy.conf << 'EOF'
# OpenVSCode代理配置 - 修复插件页面显示问题
# 针对具体网络架构: 前端(80) -> 代理(3669) -> OpenVSCode(3667)

# 上游OpenVSCode服务器
upstream openvscode_backend {
    server *************:3667;
    keepalive 32;
}

# 代理服务器配置 - 监听3667端口，映射到宿主机3669
server {
    listen 3667;
    server_name _;

    # 增加缓冲区大小处理WebSocket和大文件
    client_max_body_size 100M;
    proxy_buffering off;
    proxy_request_buffering off;

    # 日志配置
    access_log /var/log/nginx/webide-access.log;
    error_log /var/log/nginx/webide-error.log warn;

    # 主要代理配置
    location / {
        # 代理到OpenVSCode服务器
        proxy_pass http://openvscode_backend;
        
        # 基本代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 🔧 关键修复：移除阻止iframe嵌入的头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Content-Type-Options;
        
        # 🔧 添加允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *; img-src 'self' data: blob: *; font-src 'self' data: *; connect-src 'self' ws: wss: *; worker-src 'self' blob:;" always;
        
        # 🔧 特别针对插件页面的权限
        add_header Permissions-Policy "camera=*, microphone=*, geolocation=*, clipboard-read=*, clipboard-write=*" always;
        
        # 添加CORS头部支持跨域
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
        
        # 禁用缓冲以支持实时通信
        proxy_buffering off;
        proxy_cache off;
    }

    # 🔧 特别处理扩展/插件相关的路径
    location ~* /(extensions|marketplace|gallery|webview)/ {
        proxy_pass http://openvscode_backend;
        
        # 基本代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除所有限制性头部
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Content-Type-Options;
        proxy_hide_header Referrer-Policy;
        
        # 添加最宽松的安全策略
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data: blob:; font-src *; connect-src *; worker-src * blob:; child-src *; frame-src *;" always;
        
        # 禁用缓存确保最新内容
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
        
        # CORS支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "*" always;
        add_header Access-Control-Allow-Headers "*" always;
    }

    # WebSocket特殊处理
    location ~* /(websocket|socket\.io|ws)/ {
        proxy_pass http://openvscode_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "OpenVSCode Proxy OK - Extensions Fixed for *************:3669\n";
        add_header Content-Type text/plain;
    }
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}
EOF

echo "✅ Nginx 配置已更新"

# 重启相关服务
echo ""
echo "5️⃣ 重启相关服务..."

echo "重新构建并启动服务..."
docker-compose down
docker-compose build --no-cache app
docker-compose up -d

echo "等待服务启动..."
sleep 10

# 验证服务状态
echo ""
echo "6️⃣ 验证修复效果..."

# 检查nginx代理
if curl -s --connect-timeout 10 "http://*************:3669/health" | grep -q "Extensions Fixed"; then
    echo "✅ Nginx 代理配置生效"
else
    echo "⚠️ Nginx 代理可能有问题"
fi

# 检查前端
if curl -s --connect-timeout 10 "http://*************:80" > /dev/null; then
    echo "✅ 前端应用正常"
else
    echo "⚠️ 前端应用可能有问题"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "- 更新了环境变量配置 (VITE_WEBIDE_BASE_URL)"
echo "- 修复了nginx代理配置，移除iframe限制"
echo "- 特别处理了扩展/插件相关路径"
echo "- 添加了CORS支持"
echo "- 重新构建并启动了服务"
echo ""
echo "🔄 请现在测试："
echo "1. 访问前端: http://*************:80"
echo "2. 进入WebIDE页面"
echo "3. 检查插件页面是否正常显示"
echo "4. 尝试搜索和安装插件"
echo ""
echo "🔍 对比测试："
echo "- 直接访问: http://*************:3667/?tkn=tk-ynnx-llm"
echo "- 代理访问: http://*************:3669/?tkn=tk-ynnx-llm"
echo "- 前端嵌入: http://*************:80 (WebIDE页面)"
