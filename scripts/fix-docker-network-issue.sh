#!/bin/bash

echo "🔧 修复 Docker 网络访问问题"
echo "============================"

echo "🔍 问题诊断："
echo "前端应用运行在Docker容器内，无法直接访问宿主机的*************:3669"
echo "需要配置容器间网络通信"

# 检查当前状态
echo ""
echo "1️⃣ 检查当前Docker网络状态..."
docker network ls | grep ynnx
docker-compose ps

# 备份配置
echo ""
echo "2️⃣ 备份当前配置..."
BACKUP_DIR="./backup-docker-fix-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp .env "$BACKUP_DIR/"
cp docker-compose.yml "$BACKUP_DIR/"
echo "✅ 配置已备份到: $BACKUP_DIR"

# 方案1: 修改前端配置使用容器内网络
echo ""
echo "3️⃣ 方案1: 配置容器内网络访问..."

echo "更新环境变量配置..."
# 前端应用应该通过nginx容器名访问，而不是宿主机IP
sed -i 's|VITE_WEBIDE_BASE_URL=.*|VITE_WEBIDE_BASE_URL=http://nginx:3667|g' .env

echo "✅ 环境变量已更新为容器内网络"
echo "- VITE_WEBIDE_BASE_URL=http://nginx:3667"

# 方案2: 添加nginx内部代理配置
echo ""
echo "4️⃣ 创建nginx内部代理配置..."

cat > ./nginx/conf.d/internal-webide-proxy.conf << 'EOF'
# 内部WebIDE代理配置 - 解决Docker容器网络访问问题
# 这个配置让前端容器可以通过nginx容器访问OpenVSCode

# 上游OpenVSCode服务器（外部）
upstream openvscode_external {
    server *************:3667;
    keepalive 32;
}

# 内部代理服务器配置 - 供容器内前端访问
server {
    listen 3667;
    server_name nginx localhost;

    # 增加缓冲区大小
    client_max_body_size 100M;
    proxy_buffering off;
    proxy_request_buffering off;

    # 日志配置
    access_log /var/log/nginx/webide-internal-access.log;
    error_log /var/log/nginx/webide-internal-error.log warn;

    # 主要代理配置
    location / {
        # 代理到外部OpenVSCode服务器
        proxy_pass http://openvscode_external;
        
        # 基本代理头设置
        proxy_set_header Host *************;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host *************;
        proxy_set_header X-Forwarded-Port 3667;
        
        # 🔧 移除iframe限制
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        proxy_hide_header X-Content-Type-Options;
        
        # 🔧 添加允许iframe嵌入的头部
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *; img-src 'self' data: blob: *; font-src 'self' data: *; connect-src 'self' ws: wss: *; worker-src 'self' blob:;" always;
        
        # CORS支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 保持连接
        proxy_set_header Connection "keep-alive";
        
        # 禁用缓冲
        proxy_buffering off;
        proxy_cache off;
    }

    # 🔧 特别处理扩展相关路径
    location ~* /(extensions|marketplace|gallery|webview)/ {
        proxy_pass http://openvscode_external;
        
        proxy_set_header Host *************;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 移除限制
        proxy_hide_header X-Frame-Options;
        proxy_hide_header Content-Security-Policy;
        
        # 最宽松的策略
        add_header X-Frame-Options "ALLOWALL" always;
        add_header Content-Security-Policy "frame-ancestors *; default-src *; script-src * 'unsafe-inline' 'unsafe-eval'; style-src * 'unsafe-inline'; img-src * data: blob:; font-src *; connect-src *; worker-src * blob:; child-src *; frame-src *;" always;
        
        # 禁用缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    # WebSocket处理
    location ~* /(websocket|socket\.io|ws)/ {
        proxy_pass http://openvscode_external;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host *************;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health-internal {
        access_log off;
        return 200 "Internal WebIDE Proxy OK\n";
        add_header Content-Type text/plain;
    }
}
EOF

echo "✅ 内部代理配置已创建"

# 更新docker-compose配置
echo ""
echo "5️⃣ 更新docker-compose配置..."

# 确保nginx容器可以访问外部网络
cat >> docker-compose.yml << 'EOF'

  # 添加网络配置确保容器可以访问外部服务
networks:
  ynnx-network:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      config:
        - subnet: **********/16
EOF

echo "✅ Docker网络配置已更新"

# 重启服务
echo ""
echo "6️⃣ 重启服务..."
docker-compose down
docker-compose build --no-cache app
docker-compose up -d

echo "等待服务启动..."
sleep 15

# 验证修复
echo ""
echo "7️⃣ 验证修复效果..."

echo "检查内部代理..."
if docker-compose exec nginx curl -s "http://localhost:3667/health-internal" | grep -q "Internal WebIDE Proxy OK"; then
    echo "✅ 内部代理配置正常"
else
    echo "⚠️ 内部代理可能有问题"
fi

echo "检查前端应用..."
if curl -s --connect-timeout 10 "http://*************:80" > /dev/null; then
    echo "✅ 前端应用正常"
else
    echo "⚠️ 前端应用可能有问题"
fi

echo "检查容器内网络连接..."
if docker-compose exec app curl -s --connect-timeout 5 "http://nginx:3667/health-internal" > /dev/null; then
    echo "✅ 容器内网络连接正常"
else
    echo "⚠️ 容器内网络连接有问题"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "- 配置了容器内网络访问路径"
echo "- 创建了nginx内部代理"
echo "- 更新了环境变量配置"
echo "- 优化了Docker网络配置"
echo ""
echo "🔄 请现在测试："
echo "1. 访问前端: http://*************:80"
echo "2. 进入WebIDE页面"
echo "3. 检查是否不再出现403错误"
echo "4. 验证插件页面是否正常显示"
echo ""
echo "🔍 如果仍有问题："
echo "1. 检查容器日志: docker-compose logs app"
echo "2. 检查nginx日志: docker-compose logs nginx"
echo "3. 测试容器内连接: docker-compose exec app curl http://nginx:3667/health-internal"
