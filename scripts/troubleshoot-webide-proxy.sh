#!/bin/bash

echo "🔧 WebIDE代理故障排除工具"
echo "================================="

# 颜色输出函数
red() { echo -e "\033[31m$1\033[0m"; }
green() { echo -e "\033[32m$1\033[0m"; }
yellow() { echo -e "\033[33m$1\033[0m"; }
blue() { echo -e "\033[34m$1\033[0m"; }

# 检查函数
check_service() {
    local url=$1
    local name=$2
    local timeout=${3:-5}
    
    if curl -s --connect-timeout $timeout "$url" > /dev/null 2>&1; then
        green "✅ $name 正常"
        return 0
    else
        red "❌ $name 异常"
        return 1
    fi
}

echo ""
blue "🔍 1. 检查网络连接"
echo "==================="

# 检查OpenVSCode原始服务器
check_service "http://*************:3667" "OpenVSCode原始服务器 (*************:3667)"
OPENVSCODE_STATUS=$?

# 检查代理服务器
check_service "http://localhost:3668" "WebIDE代理服务器 (localhost:3668)"
PROXY_STATUS=$?

# 检查代理健康检查
check_service "http://localhost:3668/health" "代理健康检查"

echo ""
blue "🔍 2. 检查端口占用"
echo "=================="

check_port_usage() {
    local port=$1
    local name=$2
    echo -n "端口 $port ($name): "
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        process=$(lsof -Pi :$port -sTCP:LISTEN | tail -1 | awk '{print $1 " (PID: " $2 ")"}')
        green "占用 - $process"
        return 0
    else
        red "未占用"
        return 1
    fi
}

check_port_usage 3668 "WebIDE代理"
check_port_usage 80 "nginx主服务"
check_port_usage 3002 "LDAP API"

echo ""
blue "🔍 3. 检查Docker服务"
echo "==================="

if command -v docker &> /dev/null; then
    green "✅ Docker已安装"
    
    # 检查docker-compose服务
    if docker-compose ps &> /dev/null; then
        echo ""
        echo "📊 Docker Compose服务状态:"
        docker-compose ps
        
        # 检查nginx容器
        if docker-compose ps nginx | grep -q "Up"; then
            green "✅ nginx容器运行正常"
        else
            red "❌ nginx容器异常"
            echo "nginx容器日志:"
            docker-compose logs nginx | tail -10
        fi
    else
        yellow "⚠️  Docker Compose未运行或配置文件不存在"
    fi
    
    # 检查开发代理容器
    if docker ps | grep -q "ynnx-webide-proxy"; then
        green "✅ 开发代理容器运行中"
        echo "代理容器信息:"
        docker ps | grep ynnx-webide-proxy
    else
        yellow "⚠️  开发代理容器未运行"
    fi
    
else
    red "❌ Docker未安装"
fi

echo ""
blue "🔍 4. 检查配置文件"
echo "=================="

# 检查nginx代理配置
if [ -f "nginx/conf.d/webide-proxy.conf" ]; then
    green "✅ nginx代理配置存在"
    
    # 验证配置语法
    if command -v nginx &> /dev/null; then
        if nginx -t -c nginx/conf.d/webide-proxy.conf 2>/dev/null; then
            green "✅ nginx配置语法正确"
        else
            red "❌ nginx配置语法错误"
        fi
    fi
else
    red "❌ nginx代理配置缺失: nginx/conf.d/webide-proxy.conf"
fi

# 检查环境配置
if [ -f ".env" ]; then
    green "✅ .env文件存在"
    
    webide_url=$(grep "VITE_WEBIDE_BASE_URL" .env | cut -d'=' -f2)
    echo "WebIDE URL配置: $webide_url"
    
    if [[ "$webide_url" == "http://localhost:3668" ]]; then
        green "✅ 配置为localhost代理"
    else
        yellow "⚠️  配置为直接访问: $webide_url"
    fi
else
    red "❌ .env文件不存在"
fi

echo ""
blue "🔍 5. 测试代理功能"
echo "=================="

if [ $PROXY_STATUS -eq 0 ]; then
    echo "测试代理响应..."
    
    # 测试基本连接
    response=$(curl -s -w "%{http_code}" "http://localhost:3668/" -o /dev/null)
    if [[ "$response" == "200" ]] || [[ "$response" == "302" ]]; then
        green "✅ 代理基本连接正常 (HTTP $response)"
    else
        red "❌ 代理连接异常 (HTTP $response)"
    fi
    
    # 测试健康检查
    health=$(curl -s "http://localhost:3668/health" 2>/dev/null)
    if [[ "$health" == *"Proxy OK"* ]]; then
        green "✅ 代理健康检查正常"
    else
        yellow "⚠️  代理健康检查异常: $health"
    fi
else
    red "❌ 代理服务不可用，跳过功能测试"
fi

echo ""
blue "🔍 6. 诊断结果总结"
echo "=================="

if [ $OPENVSCODE_STATUS -eq 0 ] && [ $PROXY_STATUS -eq 0 ]; then
    green "✅ 所有服务正常，WebIDE代理应该可以工作"
elif [ $OPENVSCODE_STATUS -eq 0 ] && [ $PROXY_STATUS -ne 0 ]; then
    yellow "⚠️  OpenVSCode正常，但代理异常"
    echo ""
    echo "🔧 建议解决方案:"
    echo "1. 检查nginx代理配置"
    echo "2. 重启代理服务: docker-compose restart nginx"
    echo "3. 查看代理日志: docker-compose logs nginx"
elif [ $OPENVSCODE_STATUS -ne 0 ]; then
    red "❌ OpenVSCode服务器异常"
    echo ""
    echo "🔧 建议解决方案:"
    echo "1. 启动OpenVSCode服务器"
    echo "2. 检查OpenVSCode配置和网络"
    echo "3. 确认端口3667未被其他服务占用"
else
    red "❌ 所有服务都异常"
fi

echo ""
blue "📋 常用修复命令"
echo "==============="

echo "# 重启docker-compose服务"
echo "docker-compose down && docker-compose up -d"
echo ""
echo "# 重启nginx代理"
echo "docker-compose restart nginx"
echo ""
echo "# 查看代理日志"
echo "docker-compose logs -f nginx"
echo ""
echo "# 启动开发代理"
echo "./scripts/dev-with-webide-proxy.sh"
echo ""
echo "# 停止开发代理"
echo "docker stop ynnx-webide-proxy && docker rm ynnx-webide-proxy"
echo ""
echo "# 测试OpenVSCode"
echo "curl -v http://*************:3667"
echo ""
echo "# 测试代理"
echo "curl -v http://localhost:3668/health"

echo ""
blue "📞 更多帮助"
echo "==========="
echo "查看详细文档: WEBIDE_SECURITY_CONTEXT_FIX.md"
echo "问题反馈: 请提供此诊断报告的输出" 