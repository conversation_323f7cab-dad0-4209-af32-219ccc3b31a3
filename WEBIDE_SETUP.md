# Web IDE 设置文档

## 概述

本文档说明如何配置OpenVSCode服务器以支持通过iframe嵌入到YNNX AI平台中。

## 功能特性

- 🌐 基于OpenVSCode的云端开发环境
- 🔐 Token认证机制
- 📱 响应式设计，支持全屏模式
- 🛡️ 用户登录状态验证
- 🔄 自动重连和错误处理
- 🎨 与平台UI风格一致的界面

## 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# Web IDE配置
VITE_WEBIDE_BASE_URL=http://*************:3667
VITE_WEBIDE_TOKEN=tk-ynnx-llm
```

### 配置说明

- `VITE_WEBIDE_BASE_URL`: OpenVSCode服务器的基础URL
- `VITE_WEBIDE_TOKEN`: 访问OpenVSCode的认证token

## OpenVSCode服务器配置

### 1. 安装OpenVSCode服务器

```bash
# 使用Docker运行OpenVSCode服务器
docker run -it --init -p 3667:3000 \
  -e CONNECTION_TOKEN=tk-ynnx-llm \
  -e CONNECTION_SECRET=your-secret-key \
  -v "/path/to/your/workspace:/home/<USER>" \
  gitpod/openvscode-server
```

### 2. 配置iframe嵌入支持

为了让OpenVSCode服务器支持iframe嵌入，需要配置以下HTTP头：

```bash
# 在OpenVSCode服务器配置文件中添加
X-Frame-Options: SAMEORIGIN
Content-Security-Policy: frame-ancestors 'self' http://localhost:5173 http://*************:5173
```

### 3. 使用Nginx代理（推荐）

创建Nginx配置文件：

```nginx
server {
    listen 3667;
    server_name localhost;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 允许iframe嵌入
        add_header X-Frame-Options "SAMEORIGIN";
        add_header Content-Security-Policy "frame-ancestors 'self' http://localhost:5173 http://*************:5173";
    }
}
```

## 使用方法

### 1. 用户登录

用户必须先登录YNNX AI平台才能访问Web IDE功能。

### 2. 访问Web IDE

登录后，点击导航栏中的"Web IDE"链接，即可访问云端开发环境。

### 3. 功能按钮

- **刷新**: 重新加载IDE界面
- **全屏**: 切换全屏模式
- **新窗口打开**: 在新窗口中打开IDE

## 故障排除

### 1. 403 Forbidden错误 (最常见)

**问题**: 控制台显示 `GET http://*************:3667/ 403 (Forbidden)`

**原因**: OpenVSCode服务器认证配置问题

**解决方法**:

#### 方法A: 使用Docker启动（推荐）
```bash
# 运行我们提供的启动脚本
./scripts/start-openvscode-server.sh

# 或者手动运行Docker命令
docker run -it --rm \
    -p 3667:3000 \
    -e CONNECTION_TOKEN="tk-ynnx-llm" \
    -e VSCODE_PROXY_URI="http://*************:3667" \
    -v "/tmp/openvscode-workspace:/home/<USER>" \
    --name openvscode-server \
    gitpod/openvscode-server:latest
```

#### 方法B: 不使用Docker
```bash
# 运行我们提供的启动脚本
./scripts/start-openvscode-without-docker.sh

# 或者手动运行
openvscode-server \
    --host 0.0.0.0 \
    --port 3667 \
    --connection-token tk-ynnx-llm \
    --accept-server-license-terms \
    --folder /tmp/openvscode-workspace
```

#### 方法C: 修改环境变量
如果服务器使用不同的token参数，可以在`.env`文件中配置：
```env
# 更改token参数名称
VITE_WEBIDE_TOKEN_PARAM=token  # 默认是tkn

# 或者更改token值
VITE_WEBIDE_TOKEN=your-custom-token
```

### 2. 无法连接到WebIDE服务

**问题**: 显示"无法连接到WebIDE服务，请检查服务器状态"

**解决方法**:
1. 检查OpenVSCode服务器是否正在运行
2. 确认服务器地址和端口配置正确
3. 检查网络连接和防火墙设置

### 3. iframe加载失败

**问题**: IDE界面不显示或显示错误

**解决方法**:
1. 检查OpenVSCode服务器的X-Frame-Options配置
2. 确认Content-Security-Policy设置正确
3. 检查浏览器控制台是否有跨域错误

### 4. 认证失败

**问题**: 显示认证错误或拒绝访问

**解决方法**:
1. 检查环境变量中的TOKEN配置
2. 确认OpenVSCode服务器的CONNECTION_TOKEN设置
3. 验证token是否正确

### 5. 调试信息查看

在浏览器开发者工具的控制台中，查看WebIDE配置信息：
- 基础URL
- Token值
- 完整访问URL
- 认证参数格式

## 安全考虑

### 1. Token安全

- 使用复杂的token值
- 定期更换token
- 不要在代码中硬编码token

### 2. 网络安全

- 使用HTTPS（生产环境）
- 配置适当的防火墙规则
- 限制访问IP范围

### 3. 数据安全

- 定期备份工作区数据
- 设置适当的文件权限
- 监控用户活动

## 开发指南

### 1. 修改WebIDE配置

如需修改WebIDE的配置，请编辑 `src/components/WebIDESection.jsx` 文件。

### 2. 添加新功能

可以在WebIDE组件中添加以下功能：
- 项目模板选择
- 代码片段管理
- 版本控制集成
- 实时协作

### 3. 自定义样式

WebIDE使用Tailwind CSS框架，可以通过修改className来自定义样式。

## 更新日志

### v1.0.0
- 初始版本
- 支持iframe嵌入OpenVSCode
- 用户认证集成
- 响应式设计

## 许可证

本项目基于MIT许可证开源。

## 支持与反馈

如遇到问题或有改进建议，请联系开发团队。 