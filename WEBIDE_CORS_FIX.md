# WebIDE CORS问题修复

## 🚨 问题诊断

**CORS错误**：`Access to fetch at 'http://*************:3667/?tkn=tk-ynnx-llm' from origin 'http://localhost:5173' has been blocked by CORS policy`

**根本原因**：OpenVSCode服务器没有设置`Access-Control-Allow-Origin`头来允许跨域fetch请求。

## 🔧 修复方案

### 1. **完全移除fetch调用**
- 删除了所有使用fetch的预认证逻辑
- 避免CORS策略阻塞

### 2. **直接iframe加载**
```javascript
const loadIframe = () => {
  if (iframeRef.current) {
    iframeRef.current.src = url; // 直接设置src
  }
};
```

### 3. **添加加载超时机制**
- 30秒超时保护
- 超时后提示用户刷新或新窗口打开

### 4. **改进的调试信息**
- 页面显示配置信息
- 详细的控制台日志
- iframe加载状态跟踪

## 🧪 测试步骤

1. **刷新页面**并登录
2. **访问Web IDE**
3. **查看控制台日志**，应该看到：
   ```
   WebIDE组件加载，开始加载iframe
   开始加载iframe，URL: http://*************:3667?tkn=tk-ynnx-llm
   设置iframe src: http://*************:3667?tkn=tk-ynnx-llm
   iframe加载完成，当前src: http://*************:3667?tkn=tk-ynnx-llm
   WebIDE真正加载完成
   ```

4. **不应该再看到**：
   - ❌ CORS错误
   - ❌ "认证失败，请检查token配置"
   - ❌ fetch相关错误

## 🎯 预期结果

- ✅ 没有CORS错误
- ✅ iframe正常加载OpenVSCode界面
- ✅ 配置信息正确显示
- ✅ 可以使用所有控制按钮

## 🔍 如果仍有问题

### iframe加载失败
1. 检查OpenVSCode服务器是否运行：`curl "http://*************:3667?tkn=tk-ynnx-llm"`
2. 点击"新窗口打开"测试
3. 检查浏览器是否阻止iframe加载

### 空白页面
1. 查看浏览器控制台错误
2. 检查网络标签中的请求状态
3. 尝试刷新按钮

### 加载超时
1. 检查网络连接
2. 确认OpenVSCode服务器响应正常
3. 尝试在新窗口中直接访问

## 📋 技术说明

**为什么iframe不受CORS限制？**
- iframe加载是浏览器的导航行为，不是XMLHttpRequest
- 浏览器允许iframe加载跨域内容
- 只有JavaScript访问iframe内容时才受同源策略限制

**为什么移除fetch？**
- fetch/XMLHttpRequest受严格的CORS策略限制
- OpenVSCode服务器未配置跨域访问头
- 直接iframe加载更简单、更可靠 