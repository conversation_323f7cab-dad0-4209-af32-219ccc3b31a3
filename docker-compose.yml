version: '3.8'

services:
  # 应用服务
  app:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        VITE_LDAP_API_URL: http://*************:3002
        VITE_WEBIDE_BASE_URL: http://localhost:3668
        VITE_LITELLM_API_BASE: http://*************:14000
    container_name: ynnx-ai-platform
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - TZ=Asia/Shanghai
      # WebIDE配置 - 使用nginx代理解决iframe嵌入问题
      - VITE_WEBIDE_BASE_URL=http://*************:3669
      - VITE_WEBIDE_TOKEN=tk-ynnx-llm
      - VITE_WEBIDE_TOKEN_PARAM=tkn
      - LDAP_AUTH_PORT=3002
      - LDAP_DEFAULT_HOST=0.0.0.0
      - LDAP_PRIMARY_HOST=0.0.0.0
      - CORS_ORIGIN=http://*************,http://0.0.0.0
      # LDAP 基础配置
      - LDAP_DEFAULT_ENVIRONMENT=DEVVDI_ENV
      - LDAP_CONNECTION_TIMEOUT=20
      - LDAP_SEARCH_TIMEOUT=10
      - LDAP_MAX_RETRIES=2
      # DEVVDI 环境完整配置
      - LDAP_DEVVDI_ENV_URL=ldap://*************:11389
      - LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_BIND_DN=
      - LDAP_DEVVDI_ENV_BIND_PASSWORD=
      - LDAP_DEVVDI_ENV_USER_SEARCH_BASE=DC=DEVVDI,DC=YNRCC,DC=COM
      - LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
      - LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USER_DOMAIN=@DEVVDI.YNRCC.COM
      - LDAP_DEVVDI_ENV_USE_DIRECT_BIND=true
      - LDAP_DEVVDI_ENV_NAME=240.10云桌面环境
      - LDAP_DEVVDI_ENV_DESCRIPTION=DEVVDI Active Directory环境 - 端口389
      # VDI 环境完整配置
      - LDAP_VDI_ENV_URL=ldap://*************:12389
      - LDAP_VDI_ENV_BASE_DN=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_BIND_DN=
      - LDAP_VDI_ENV_BIND_PASSWORD=
      - LDAP_VDI_ENV_USER_SEARCH_BASE=DC=VDI,DC=YNNX,DC=COM
      - LDAP_VDI_ENV_USER_FILTER=(userPrincipalName={{username}}@VDI.YNNX.COM)
      - LDAP_VDI_ENV_USER_DN_PATTERN={{username}}@VDI.YNNX.COM
      - LDAP_VDI_ENV_USER_DOMAIN=@VDI.YNNX.COM
      - LDAP_VDI_ENV_USE_DIRECT_BIND=true
      - LDAP_VDI_ENV_NAME=242.2云桌面环境
      - LDAP_VDI_ENV_DESCRIPTION=VDI Active Directory环境 - 端口389
      # LLM 配置
      - OPENAI_BASE_URL=http://*************:14000/v1
      - OPENAI_MODEL=qwen3-235b-a22b
      - OPENAI_API_KEY=sk-ynnx-llm-20250530
      - ENABLE_OPENAI=true
      - LITELLM_MASTER_KEY=sk-ynnx-llm-20250530
    volumes:
      - ./logs:/var/log/ynnx-ai
      - ./dist:/app/dist
    ports:
      - "3002:3002"
    networks:
      - ynnx-network

  # Nginx反向代理 - 扩展支持OpenVSCode代理
  nginx:
    image: nginx:alpine
    container_name: ynnx-nginx
    restart: unless-stopped
    ports:
      - "80:80"        # Web应用
      - "3669:3667"    # OpenVSCode HTTP代理端口（映射到3669避免冲突）
      - "3668:3668"    # OpenVSCode HTTPS代理端口
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/conf.d/webide-proxy.conf:/etc/nginx/conf.d/webide-proxy.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./dist:/usr/share/nginx/html:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - ynnx-network
    depends_on:
      - app

  # OpenVSCode Server (可选，如果需要在docker-compose中管理)
  # 注释掉，因为你可能在外部运行OpenVSCode
  # openvscode:
  #   image: gitpod/openvscode-server:latest
  #   container_name: ynnx-openvscode
  #   restart: unless-stopped
  #   environment:
  #     - OPENVSCODE_SERVER_ROOT=/home/<USER>
  #     - CONNECTION_TOKEN=tk-ynnx-llm
  #   volumes:
  #     - ./workspace:/home/<USER>
  #   ports:
  #     - "3668:3000"  # 内部端口，通过nginx代理访问
  #   networks:
  #     - ynnx-network

networks:
  ynnx-network:
    driver: bridge 