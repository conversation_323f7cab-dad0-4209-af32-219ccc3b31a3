# Web IDE 403错误解决指南

## 🚨 问题描述

当访问Web IDE时，浏览器控制台显示：
```
GET http://*************:3667/ 403 (Forbidden)
```

## 🔍 问题原因

403错误通常由以下原因引起：
1. **OpenVSCode服务器未正确配置**
2. **Token认证失败**
3. **服务器未设置正确的认证参数**

## 🛠️ 解决方案

### 步骤1: 测试当前配置

首先运行测试脚本来诊断问题：

```bash
./scripts/test-openvscode-server.sh
```

### 步骤2: 启动正确配置的OpenVSCode服务器

#### 选项A: 使用Docker（推荐）

```bash
# 运行启动脚本
./scripts/start-openvscode-server.sh

# 或者手动运行Docker命令
docker run -it --rm \
    -p 3667:3000 \
    -e CONNECTION_TOKEN="tk-ynnx-llm" \
    -e VSCODE_PROXY_URI="http://*************:3667" \
    -v "/tmp/openvscode-workspace:/home/<USER>" \
    --name openvscode-server \
    gitpod/openvscode-server:latest
```

#### 选项B: 不使用Docker

```bash
# 运行启动脚本
./scripts/start-openvscode-without-docker.sh

# 或者手动运行
openvscode-server \
    --host 0.0.0.0 \
    --port 3667 \
    --connection-token tk-ynnx-llm \
    --accept-server-license-terms \
    --folder /tmp/openvscode-workspace
```

### 步骤3: 验证配置

再次运行测试脚本：

```bash
./scripts/test-openvscode-server.sh
```

你应该看到：
- ✅ 服务器正在运行
- ✅ Token认证成功 (HTTP 200)
- ✅ 端口 3667 可访问

### 步骤4: 刷新Web IDE页面

在浏览器中刷新Web IDE页面，现在应该可以正常加载。

## 🔧 高级配置

### 自定义Token参数

如果你的OpenVSCode服务器使用不同的token参数名称，可以在`.env`文件中配置：

```env
# 默认配置
VITE_WEBIDE_BASE_URL=http://*************:3667
VITE_WEBIDE_TOKEN=tk-ynnx-llm
VITE_WEBIDE_TOKEN_PARAM=tkn

# 如果服务器使用 'token' 参数
VITE_WEBIDE_TOKEN_PARAM=token

# 如果服务器使用 'access_token' 参数
VITE_WEBIDE_TOKEN_PARAM=access_token
```

### 修改服务器地址

如果OpenVSCode服务器在不同的地址运行：

```env
# 修改为实际的服务器地址
VITE_WEBIDE_BASE_URL=http://your-server-ip:port
```

## 🐛 常见问题

### Q1: Docker命令执行失败

**问题**: `docker: command not found`

**解决**: 
- 安装Docker或使用不使用Docker的启动脚本
- 运行: `./scripts/start-openvscode-without-docker.sh`

### Q2: 端口已被占用

**问题**: `port 3667 already in use`

**解决**:
```bash
# 查找占用端口的进程
lsof -i :3667

# 停止占用端口的进程
sudo kill -9 <PID>

# 或者修改端口
docker run -it --rm -p 3668:3000 ...
```

### Q3: 仍然显示403错误

**问题**: 即使服务器正常启动，仍然显示403

**解决**:
1. 检查浏览器开发者工具的网络标签
2. 确认请求的URL格式正确
3. 检查服务器日志
4. 尝试在新窗口中直接访问: `http://*************:3667?tkn=tk-ynnx-llm`

## 📝 验证步骤

1. ✅ 运行测试脚本无错误
2. ✅ 浏览器控制台无403错误
3. ✅ Web IDE界面正常加载
4. ✅ 可以创建和编辑文件

## 📞 获取帮助

如果按照以上步骤仍无法解决问题，请：

1. 收集以下信息：
   - 测试脚本的完整输出
   - 浏览器开发者工具的控制台错误
   - OpenVSCode服务器的启动日志

2. 检查详细文档：`WEBIDE_SETUP.md`

3. 联系技术支持团队

## 🎉 成功标志

当看到以下信息时，表示配置成功：
- 浏览器控制台显示：`WebIDE配置: { baseUrl: "http://*************:3667", token: "tk-ynnx-llm", tokenParam: "tkn", url: "http://*************:3667?tkn=tk-ynnx-llm" }`
- Web IDE界面正常加载
- 可以看到VS Code的文件浏览器和编辑器界面 