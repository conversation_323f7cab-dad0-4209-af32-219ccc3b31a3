import React from 'react';
import { useCopilotReadable, useCopilotAction } from '@copilotkit/react-core';
import { Markdown } from '@copilotkit/react-ui';

// 自定义AssistantMessage组件，用于过滤deepseek-r1模型的思考内容
// 
// 功能说明:
// 1. 自动移除AI回答中的 <think>...</think> 标签及其内容
// 2. 清理多余的空行和空白字符
// 3. 如果过滤后内容为空，显示默认提示信息
// 4. 保持与官方CopilotKit样式的一致性
//
// 测试方法:
// 在聊天中输入 "测试思考内容过滤" 或 "testThinkingFilter" 来验证功能
const CustomAssistantMessage = ({ message, isLoading, subComponent }) => {
  // 过滤掉<think></think>标签中的内容
  const filterThinkingContent = (text) => {
    if (!text) return text;
    
    // 使用正则表达式移除<think>...</think>标签及其内容
    // 支持多行、嵌套、以及各种空白字符
    let filteredText = text
      .replace(/<think>[\s\S]*?<\/think>/gi, '') // 移除think标签及内容
      .replace(/^\s*\n/gm, '') // 移除空行
      .trim(); // 移除首尾空白
    
    // 如果过滤后内容为空，返回默认消息
    if (!filteredText || filteredText.length === 0) {
      return "我正在为您处理这个问题...";
    }
    
    return filteredText;
  };

  const filteredMessage = filterThinkingContent(message);

  return (
    <div className="copilotKitAssistantMessage">
      {filteredMessage && <Markdown content={filteredMessage} />}
      {isLoading && (
        <div className="flex items-center gap-1 text-gray-500 mt-2">
          <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="text-sm">AI正在思考中...</span>
        </div>
      )}
      {subComponent && <div className="mt-2">{subComponent}</div>}
    </div>
  );
};

const CopilotProvider = ({ user, children }) => {
  
  // 核心平台信息 - 与实际页面内容完全一致
  useCopilotReadable({
    description: "YNNX AI开发平台核心信息",
    value: {
      platform: "YNNX AI开发平台",
      organization: "云南农信",
      tagline: "云南农信智能开发助手",
      userStatus: user ? { loggedIn: true, username: user.username } : { loggedIn: false },
      coreFeatures: [
        {
          name: "AI智能对话",
          description: "基于先进的大语言模型，提供自然流畅的AI对话体验。支持代码生成、问题解答、创意写作等多种场景",
          benefits: ["支持多种AI模型", "实时响应速度", "上下文理解", "创意内容生成"]
        },
        {
          name: "API密钥管理", 
          description: "企业级API密钥管理系统，提供安全的密钥存储、权限控制和使用监控",
          benefits: ["企业级安全", "使用量监控", "快速配置", "自动轮换"]
        },
        {
          name: "开发工具集成",
          description: "完整的开发工具生态系统，包括VS Code、JetBrains等主流IDE插件。提供代码补全、智能重构、文档生成等功能",
          benefits: ["IDE原生集成", "代码智能补全", "自动文档生成", "项目模板生成"]
        }
      ],
      useCases: [
        "软件开发 - 代码生成与调试、技术文档编写、架构设计咨询、代码审查优化",
        "数据分析 - SQL查询生成、数据可视化建议、报表模板创建、业务洞察分析", 
        "学习培训 - 编程教学辅助、技术答疑解惑、最佳实践指导、项目实战演练",
        "创新研发 - 原型快速开发、算法优化建议、技术调研支持、创意方案生成"
      ],
      latestNews: [
        {
          title: "YNNX AI DEVELOPER 1.0 发布",
          type: "重要公告",
          date: "2025-06-18",
          description: "全新基于AI的开发助手平台，支持多种编程语言，支持多种开发工具"
        },
        {
          title: "重磅预告：三大核心应用即将发布",
          type: "预告",
          date: "2025-07-08",
          description: "近期即将推出 Web IDE 在线开发环境、Web Chat 智能对话系统及 DeepWiki 知识库应用"
        },
        {
          title: "YNNX AI DEVELOPER 1.0 开启试运行", 
          type: "新闻",
          date: "2025-06-18",
          description: "欢迎各位技术大咖一起加入，近期将陆续发布专属IDE、DeepWiki等更多功能"
        }
      ]
    }
  });

  // 当前页面导航信息 - 简化版
  useCopilotReadable({
    description: "页面导航和可用区域",
    value: {
      currentPage: "主页",
      availableSections: {
        "home": "首页 - 平台介绍",
        "features": "功能特性 - 平台能力展示", 
        "ai-assistant": "AI智能助手 - AI服务演示",
        "api-key": "API密钥管理 - 密钥生成和管理",
        "downloads": "工具下载 - IDE插件下载",
        "docs": "文档中心 - 使用指南和教程",
        "news": "最新动态 - 平台更新和新闻"
      }
    }
  });

  // 文档资源库 - 用于AI查询
  useCopilotReadable({
    description: "平台文档和知识库",
    value: {
      documentation: {
        cline: {
          description: "VS Code AI编程助手插件",
          installation: "下载.vsix文件后在VS Code中安装",
          configuration: "需要配置API地址和密钥",
          features: ["代码生成", "文件操作", "终端命令", "智能问答"]
        },
        jetbrains: {
          description: "JetBrains IDE AI助手",
          installation: "下载插件包后在IDE中安装",
          features: ["智能代码补全", "重构建议", "测试生成"]
        },
        apiKeys: {
          description: "API密钥管理服务",
          usage: "用于连接IDE插件和AI服务",
          security: "安全加密存储，支持动态生成和撤销"
        }
      },
      commonQuestions: [
        "如何安装Cline插件？",
        "如何配置API密钥？",
        "插件连接失败怎么办？",
        "平台支持哪些IDE？"
      ]
    }
  });

  // 增强的智能导航action
  useCopilotAction({
    name: "navigateToSection",
    description: "导航到平台的指定区域。支持中文关键词智能识别，如：API密钥、工具下载、文档中心、AI助手、功能特性、首页、最新动态",
    parameters: [
      {
        name: "target",
        type: "string",
        description: "目标区域，可以是英文ID或中文名称",
        required: true
      }
    ],
    handler: ({ target }) => {
      console.log(`[CopilotKit] 导航请求: "${target}"`);
      
      // 中文到英文ID的映射
      const sectionMapping = {
        // 首页相关
        '首页': 'home', '主页': 'home', 'home': 'home',
        
        // 功能特性相关  
        '功能': 'features', '功能特性': 'features', '功能介绍': 'features', 
        '平台功能': 'features', 'features': 'features',
        
        // AI助手相关
        'AI助手': 'ai-assistant', 'ai助手': 'ai-assistant', 'AI智能助手': 'ai-assistant',
        'ai智能助手': 'ai-assistant', '智能助手': 'ai-assistant', '助手': 'ai-assistant',
        'ai-assistant': 'ai-assistant',
        
        // API密钥相关
        'API密钥': 'api-key', 'api密钥': 'api-key', 'API密钥管理': 'api-key',
        'api密钥管理': 'api-key', '密钥': 'api-key', '密钥管理': 'api-key',
        'api-key': 'api-key',
        
        // 工具下载相关
        '工具下载': 'downloads', '下载': 'downloads', '工具': 'downloads',
        '插件下载': 'downloads', 'downloads': 'downloads',
        
        // 文档相关
        '文档': 'docs', '文档中心': 'docs', '帮助': 'docs', 
        '说明': 'docs', '指南': 'docs', 'docs': 'docs',
        
        // 新闻动态相关
        '最新动态': 'news', '动态': 'news', '新闻': 'news', 'news': 'news'
      };

      const sectionTitles = {
        home: "首页",
        features: "功能特性", 
        "ai-assistant": "AI智能助手",
        "api-key": "API密钥管理",
        downloads: "工具下载",
        docs: "文档中心",
        news: "最新动态"
      };

      // 智能匹配目标区域
      let targetSection = null;
      const normalizedTarget = target.trim();
      
      // 1. 直接匹配
      if (sectionMapping[normalizedTarget]) {
        targetSection = sectionMapping[normalizedTarget];
      }
      // 2. 包含匹配 - 处理复合关键词
      else {
        for (const [keyword, sectionId] of Object.entries(sectionMapping)) {
          if (normalizedTarget.includes(keyword) || keyword.includes(normalizedTarget)) {
            targetSection = sectionId;
            break;
          }
        }
      }

      console.log(`[CopilotKit] 映射结果: "${target}" -> "${targetSection}"`);

      if (!targetSection) {
        console.warn(`[CopilotKit] 未找到匹配的区域: "${target}"`);
        return `未找到"${target}"对应的页面区域。可用区域：首页、功能特性、AI智能助手、API密钥管理、工具下载、文档中心、最新动态`;
      }

      // 查找DOM元素
      const element = document.getElementById(targetSection);
      console.log(`[CopilotKit] 查找元素 ID="${targetSection}":`, element ? '找到' : '未找到');
      
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
        console.log(`[CopilotKit] 导航成功到: ${sectionTitles[targetSection]}`);
        return `✅ 已成功导航到${sectionTitles[targetSection]}区域`;
      } else {
        console.error(`[CopilotKit] DOM元素不存在: ID="${targetSection}"`);
        return `❌ 页面区域"${sectionTitles[targetSection]}"暂时不可用，请稍后再试`;
      }
    },
    render: ({ status, args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            🧭 正在导航到 "{args.target}"...
          </div>
        );
      }
      if (status === 'complete') {
        const isSuccess = result.includes('✅');
        return (
          <div className={`flex items-center gap-2 p-3 rounded-lg ${
            isSuccess ? 'text-green-600 bg-green-50' : 'text-orange-600 bg-orange-50'
          }`}>
            {result}
          </div>
        );
      }
      return null;
    }
  });

  // 用户认证action
  useCopilotAction({
    name: "showLogin",
    description: "显示登录窗口，当用户需要访问受保护的功能时使用",
    handler: () => {
      if (user) {
        return "您已经登录了";
      }
      window.dispatchEvent(new CustomEvent('showLoginModal'));
      return "已打开登录窗口";
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-2 rounded">
            🔐 正在打开登录窗口...
          </div>
        );
      }
      if (status === 'complete') {
        return (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 p-2 rounded">
            ✅ {result}
          </div>
        );
      }
      return null;
    }
  });

  // 文档查询action - 智能文档检索
  useCopilotAction({
    name: "queryDocumentation",
    description: "查询平台文档和使用指南，回答用户关于工具使用、配置、安装等问题",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "用户的问题或需要查询的内容",
        required: true
      },
      {
        name: "category",
        type: "string",
        description: "文档类别",
        enum: ["cline", "jetbrains", "api-keys", "general"],
        required: false
      }
    ],
    handler: ({ query, category }) => {
      // 简化的文档检索逻辑
      const docs = {
        cline: {
          installation: "1. 登录平台下载.vsix文件\n2. 打开VS Code\n3. 按Ctrl+Shift+P，搜索'Install from VSIX'\n4. 选择下载的文件进行安装",
          configuration: "1. 打开Cline插件设置\n2. 配置API地址: http://*************:14000\n3. 输入您的API密钥\n4. 测试连接",
          usage: "1. 点击VS Code左侧Cline图标\n2. 在聊天框中描述需求\n3. 查看AI生成的代码\n4. 确认后应用更改",
          troubleshooting: "常见问题：\n- 连接失败：检查API地址和密钥\n- 无响应：确认网络连接\n- 安装失败：确认VS Code版本兼容性"
        },
        jetbrains: {
          installation: "1. 下载JetBrains AI助手插件\n2. 在IDE中打开插件管理\n3. 选择本地安装插件\n4. 重启IDE",
          configuration: "在IDE设置中配置AI助手连接参数",
          features: "智能代码补全、重构建议、测试生成、代码解释"
        },
        apiKeys: {
          generation: "1. 登录平台\n2. 进入API密钥管理\n3. 点击生成新密钥\n4. 保存密钥（仅显示一次）",
          usage: "在IDE插件中配置API密钥以连接AI服务",
          security: "密钥采用加密存储，支持随时撤销和重新生成"
        }
      };

      // 智能匹配用户查询
      const queryLower = query.toLowerCase();
      let response = "我来为您查找相关文档信息：\n\n";

      if (queryLower.includes('安装') || queryLower.includes('install')) {
        if (category === 'cline' || queryLower.includes('cline')) {
          response += "**Cline插件安装指南：**\n" + docs.cline.installation;
        } else if (category === 'jetbrains' || queryLower.includes('jetbrains')) {
          response += "**JetBrains插件安装指南：**\n" + docs.jetbrains.installation;
        } else {
          response += "**插件安装指南：**\n" + docs.cline.installation;
        }
      } else if (queryLower.includes('配置') || queryLower.includes('设置') || queryLower.includes('config')) {
        if (queryLower.includes('api') || queryLower.includes('密钥')) {
          response += "**API密钥配置：**\n" + docs.apiKeys.generation;
        } else {
          response += "**Cline配置指南：**\n" + docs.cline.configuration;
        }
      } else if (queryLower.includes('使用') || queryLower.includes('怎么用') || queryLower.includes('how to')) {
        response += "**Cline使用方法：**\n" + docs.cline.usage;
      } else if (queryLower.includes('问题') || queryLower.includes('错误') || queryLower.includes('failed')) {
        response += "**故障排除：**\n" + docs.cline.troubleshooting;
      } else {
        response += "为您提供相关信息：\n";
        if (queryLower.includes('cline')) {
          response += "**Cline插件概述：**\nVS Code AI编程助手，支持代码生成、文件操作等功能\n\n";
          response += "**快速开始：**\n" + docs.cline.installation;
        } else if (queryLower.includes('api') || queryLower.includes('密钥')) {
          response += "**API密钥服务：**\n" + docs.apiKeys.usage;
        } else {
          response += "请具体说明您需要了解的内容，比如：\n- Cline插件安装\n- API密钥配置\n- 使用教程\n- 问题排除";
        }
      }

      return response;
    },
    render: ({ status, args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded">
            📚 正在查询文档：{args.query}...
          </div>
        );
      }
      if (status === 'complete') {
        return (
          <div className="bg-gray-50 border-l-4 border-blue-500 p-4 my-2">
            <div className="font-medium text-gray-800 mb-2">📚 文档查询结果</div>
            <div className="text-sm text-gray-700 whitespace-pre-line">
              {result}
            </div>
          </div>
        );
      }
      return null;
    }
  });

  // 平台功能介绍action - 基于真实页面内容，防止幻觉
  useCopilotAction({
    name: "introducePlatformFeatures",
    description: "介绍YNNX AI开发平台的功能特性，回答关于平台有什么功能、能做什么等问题",
    handler: () => {
      return {
        platformName: "YNNX AI开发平台",
        tagline: "云南农信智能开发助手",
        coreFeatures: [
          {
            name: "🤖 AI智能对话",
            description: "基于先进的大语言模型，提供自然流畅的AI对话体验",
            capabilities: ["代码生成", "问题解答", "创意写作", "技术咨询"],
            benefits: ["支持多种AI模型", "实时响应速度", "上下文理解", "创意内容生成"]
          },
          {
            name: "🔐 API密钥管理", 
            description: "企业级API密钥管理系统，安全便捷的密钥服务",
            capabilities: ["密钥生成", "权限控制", "使用监控", "安全存储"],
            benefits: ["企业级安全", "使用量监控", "快速配置", "自动轮换"]
          },
          {
            name: "🛠️ 开发工具集成",
            description: "完整的开发工具生态系统，支持主流IDE插件",
            capabilities: ["VS Code插件", "JetBrains插件", "代码补全", "智能重构"],
            benefits: ["IDE原生集成", "代码智能补全", "自动文档生成", "项目模板生成"]
          }
        ],
        useCases: [
          "💻 软件开发 - 代码生成与调试、技术文档编写、架构设计咨询",
          "📊 数据分析 - SQL查询生成、数据可视化建议、报表模板创建", 
          "🎓 学习培训 - 编程教学辅助、技术答疑解惑、最佳实践指导",
          "🚀 创新研发 - 原型快速开发、算法优化建议、技术调研支持"
        ]
      };
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            📋 正在准备平台功能介绍...
          </div>
        );
      }
      if (status === 'complete' && result) {
        return (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6 my-3">
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-gray-800">{result.platformName}</h3>
              <p className="text-sm text-gray-600">{result.tagline}</p>
            </div>
            
            <div className="space-y-4">
              <div className="font-medium text-gray-800 mb-3">🌟 核心功能</div>
              {result.coreFeatures.map((feature, index) => (
                <div key={index} className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="font-semibold text-gray-800 mb-2">{feature.name}</div>
                  <div className="text-sm text-gray-600 mb-2">{feature.description}</div>
                  <div className="text-xs text-gray-500">
                    <strong>能力：</strong>{feature.capabilities.join(" • ")}
                  </div>
                </div>
              ))}
              
              <div className="mt-4">
                <div className="font-medium text-gray-800 mb-2">🎯 应用场景</div>
                <div className="text-sm text-gray-600 space-y-1">
                  {result.useCases.map((useCase, index) => (
                    <div key={index} className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      {useCase}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      }
      return null;
    }
  });

  // 最新动态查询action - 基于真实NewsSection内容
  useCopilotAction({
    name: "queryLatestNews",
    description: "查询平台最新动态、新闻公告和更新信息，当用户询问最新动态、新闻、公告时使用",
    handler: () => {
      // 基于真实NewsSection组件的内容
      const latestNews = [
        {
          id: 1,
          type: '重要公告',
          title: 'YNNX AI DEVELOPER 1.0 发布',
          description: '全新基于AI的开发助手平台，支持多种编程语言，支持多种开发工具。为开发者提供更高效的开发体验。',
          date: '2025-06-18',
          highlight: true,
          category: 'announcement'
        },
        {
          id: 5,
          type: '预告',
          title: '重磅预告：三大核心应用即将发布',
          description: '近期即将推出 Web IDE 在线开发环境、Web Chat 智能对话系统及 DeepWiki 知识库应用，为开发者提供完整的云端开发生态系统。敬请期待！',
          date: '2025-07-08',
          highlight: false,
          category: 'preview'
        },
        {
          id: 2,
          type: '新闻',
          title: 'YNNX AI DEVELOPER 1.0 开启试运行',
          description: '欢迎各位技术大咖一起加入我们，共同打造一个更加高效的开发环境。近期将陆续发布包括专属IDE, DeepWiki等更多功能，敬请期待。由于算力限制，目前暂提供qwen3-235b-a22b模型，生产环境则提供deepseek-v3-0324以及deepseek-r1, 后续视使用情况进行调整。',
          date: '2025-06-18',
          category: 'news'
        }
      ];

      return {
        platform: "YNNX AI开发平台",
        totalNews: latestNews.length,
        latestUpdate: "2025-06-18",
        news: latestNews
      };
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-3 rounded-lg">
            <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            📰 正在获取最新动态...
          </div>
        );
      }
      if (status === 'complete' && result) {
        return (
          <div className="bg-gradient-to-r from-cyan-50 to-blue-50 border border-cyan-200 rounded-xl p-6 my-3">
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-gray-800 flex items-center justify-center gap-2">
                📰 {result.platform} - 最新动态
              </h3>
              <p className="text-sm text-gray-600">
                最后更新：{result.latestUpdate} | 共 {result.totalNews} 条动态
              </p>
            </div>
            
            <div className="space-y-4">
              {result.news.map((item) => (
                <div key={item.id} className={`bg-white rounded-lg p-4 shadow-sm border-l-4 ${
                  item.highlight 
                    ? 'border-red-400 bg-gradient-to-r from-red-50 to-orange-50' 
                    : 'border-blue-400'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`text-xs font-semibold px-2 py-1 rounded-full text-white ${
                      item.category === 'announcement' ? 'bg-red-500' : 'bg-green-500'
                    }`}>
                      {item.type}
                    </span>
                    <span className="text-xs text-gray-500">📅 {item.date}</span>
                  </div>
                  
                  <h4 className={`font-semibold text-gray-800 mb-2 ${
                    item.highlight ? 'text-lg' : 'text-base'
                  }`}>
                    {item.title}
                  </h4>
                  
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {item.description}
                  </p>
                  
                  {item.highlight && (
                    <div className="mt-2 text-xs text-red-600 font-medium flex items-center gap-1">
                      🔥 重要更新
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                💡 想查看更多动态？可以说"带我去最新动态页面"
              </p>
            </div>
          </div>
        );
      }
      return null;
    }
  });

  // 快速帮助action
  useCopilotAction({
    name: "quickHelp",  
    description: "提供快速帮助和指引，解答常见问题",
    parameters: [
      {
        name: "helpType",
        type: "string",
        description: "帮助类型",
        enum: ["start", "download", "config", "troubleshoot"],
        required: true
      }
    ],
    handler: ({ helpType }) => {
      const helpContent = {
        start: {
          title: "快速开始",
          content: "欢迎使用YNNX AI开发平台！\n\n建议步骤：\n1. 浏览平台功能特性\n2. 注册登录账户\n3. 生成API密钥\n4. 下载IDE插件\n5. 开始AI编程"
        },
        download: {
          title: "工具下载",
          content: "可下载工具：\n\n• Cline - VS Code AI编程助手\n• JetBrains AI助手 - IDE智能插件\n\n下载后需要配置API密钥才能使用"
        },
        config: {
          title: "配置指南", 
          content: "配置步骤：\n\n1. 获取API密钥\n2. 在插件设置中填入密钥\n3. 配置API地址\n4. 测试连接\n\n如有问题请查看详细文档"
        },
        troubleshoot: {
          title: "问题排除",
          content: "常见问题解决：\n\n• 连接失败 → 检查网络和API地址\n• 无响应 → 验证API密钥\n• 安装失败 → 确认软件版本兼容性\n\n仍有问题可查询详细文档或联系支持"
        }
      };

      return helpContent[helpType];
    },
    render: ({ status, args, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-2 rounded">
            🔍 正在准备帮助信息...
          </div>
        );
      }
      if (status === 'complete' && result) {
        return (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-2">
            <div className="font-semibold text-yellow-800 mb-2">💡 {result.title}</div>
            <div className="text-sm text-gray-700 whitespace-pre-line">
              {result.content}
            </div>
          </div>
        );
      }
      return null;
    }
  });

  // 平台状态检查action
  useCopilotAction({
    name: "checkStatus",
    description: "检查平台和用户状态",
    handler: () => {
      return {
        platform: "运行正常 ✅",
        user: user ? `已登录: ${user.username}` : "未登录",
        services: ["AI智能助手", "API密钥管理", "工具下载"],
        recommendation: user ? "所有功能可用，可以开始使用" : "建议先登录以获得完整功能"
      };
    },
    render: ({ status, result }) => {
      if (status === 'executing') {
        return (
          <div className="flex items-center gap-2 text-blue-600 bg-blue-50 p-2 rounded">
            📊 正在检查状态...
          </div>
        );
      }
      if (status === 'complete' && result) {
        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 my-2">
            <div className="font-medium text-gray-800 mb-2">📊 平台状态</div>
            <div className="text-sm text-gray-700 space-y-1">
              <div>🖥️ 平台: {result.platform}</div>
              <div>👤 用户: {result.user}</div>
              <div>🔧 服务: {result.services.join(", ")}</div>
              <div className="mt-2 text-blue-600 bg-blue-100 p-2 rounded text-xs">
                💡 {result.recommendation}
              </div>
            </div>
          </div>
        );
      }
      return null;
    }
  });

  return children;
};

export default CopilotProvider;
export { CustomAssistantMessage };
