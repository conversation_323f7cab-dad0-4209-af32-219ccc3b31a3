{"permissions": {"allow": ["Bash(rg:*)", "Bash(npm install:*)", "Bash(npm run lint)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(npm run dev:full:*)", "<PERSON><PERSON>(claude doctor)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/generate-ssl-certs.sh:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(sudo mkdir:*)", "Bash(sudo chmod:*)", "Bash(./scripts/setup-webide-ssl.sh:*)", "Bash(lsof:*)", "Bash(unset ALL_PROXY)"], "deny": []}}